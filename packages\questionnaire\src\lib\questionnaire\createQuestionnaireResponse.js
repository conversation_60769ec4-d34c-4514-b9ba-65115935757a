import { decorateWithScores } from './Scoring/Scoring';
import { decorateWithHtmlReport } from './utility/htmlReportUtility';

function createStatus(status) {
  let finalStatus;
  switch (status) {
    case 'in-progress':
    case 'completed':
    case 'amended':
    case 'entered-in-error':
    case 'stopped':
      finalStatus = status;
      break;
    default:
      finalStatus = 'unknown';
  }
  return finalStatus;
}

function createIdentity(apiQuestionnaireResponse) {
  let identityArray = [];

  if (
    apiQuestionnaireResponse !== undefined &&
    apiQuestionnaireResponse.objectId !== undefined &&
    apiQuestionnaireResponse.objectId.globalId !== undefined
  ) {
    identityArray.push({
      system: 'urn:uuid',
      value: apiQuestionnaireResponse.objectId.globalId,
    });
  }

  return identityArray;
}

function createAnswer(question, answer) {
  let answerArray = [];
  if (Array.isArray(answer.answer)) {
    answer.answer.forEach((item) => {
      answerArray.push(item);
    });
  } else {
    answerArray.push(answer.answer);
  }
  const structure = {
    id: question.id,
    linkId: question.linkId,
    definition: question.definition,
    extension: [
      ...question.extension,
      {
        url: 'Item/question-type',
        valueString: question.type,
      },
    ],
    text: question.text,
    answer: answerArray,
    // type: question.type,
  };
  return structure;
}

function isQuestionAnswered(answer) {
  let isAnswered = false;
  if (answer !== undefined && answer.answer !== undefined) {
    if (Array.isArray(answer.answer) && answer.answer.length) {
      isAnswered = true;
    } else if (typeof answer.answer === 'object') {
      if (
        answer.answer.valueCoding ||
        answer.answer.valueString ||
        !isNaN(answer.answer.valueInteger) ||
        answer.answer.valueDate ||
        answer.answer.valueDateTime ||
        !isNaN(answer.answer.valueDecimal)
      ) {
        isAnswered = true;
      }
    }
  }
  return isAnswered;
}

export function buildEmptyGridQuestionResponse(questionType) {
  let response = {};
  if (questionType === 'text') {
    response = {
      valueString: null,
    };
  } else if (questionType === 'dateTime' || questionType === 'date') {
    response = {
      valueDate: null,
    };
  } else if (questionType === 'decimal') {
    response = {
      valueInteger: null,
    };
  } else {
    console.log('handle question type: ', questionType);
  }
  return response;
}

function createGridQuestionAnswer(fhirQuestionnaireItems, answers) {
  let noOfRows = 0;
  let questionAnswerList = [];

  for (let question of fhirQuestionnaireItems.item) {
    const responses = answers.get(question.linkId) && answers.get(question.linkId).answer; // use linkId for grid columns to get answer
    if (noOfRows < responses.length) {
      noOfRows = responses.length;
    }
  }

  for (let index = 0; index < noOfRows; index++) {
    let subQuestionAnswerList = {
      linkId: fhirQuestionnaireItems.linkId,
      item: [],
    };
    fhirQuestionnaireItems.item.forEach((question) => {
      const responses = answers.get(question.linkId);
      if (responses?.answer) {
        subQuestionAnswerList = {
          ...subQuestionAnswerList,
          item: [
            ...subQuestionAnswerList.item,
            {
              id: question?.id,
              linkId: question.linkId,
              text: question.text,
              extension: [
                ...question.extension,
                {
                  url: 'Item/row-sequence',
                  valueInteger: index + 1,
                },
                {
                  url: 'Item/question-type',
                  valueString: question.type,
                },
              ],
              answer: responses.answer[index]
                ? [responses.answer[index]]
                : [buildEmptyGridQuestionResponse(question.type)],
            },
          ],
        };
      }
    });

    questionAnswerList.push(subQuestionAnswerList);
  }

  return questionAnswerList;
}

function createAnswers(fhirQuestionnaireItems, answers) {
  let answerList = [];
  fhirQuestionnaireItems.map((question, index) => {
    if (question.type === 'group') {
      let groupAnswers = createAnswers(question.item, answers);
      if (groupAnswers.length > 0) {
        if (question.id && question.id.includes('complex')) {
          question.item.forEach((subQuestion) => {
            answerList.push({
              id: question.id,
              linkId: question.linkId,
              extension: [
                ...question.extension,
                {
                  url: 'Item/question-type',
                  valueString: subQuestion.type,
                },
              ],
              text: question.text,
              item: createGridQuestionAnswer(subQuestion, answers),
              // type: 'group',
            });
          });
        } else {
          console.log(question, 'extension', question.extension);
          answerList.push({
            id: question.id,
            linkId: question.linkId,
            extension: [
              ...(question.extension || []),
              {
                url: 'Item/question-type',
                valueString: 'group',
              },
            ],
            item: groupAnswers,
            // type: 'group',
          });
        }
      }
    } else {
      let answer = answers.get(question.id) || answers.get(question.linkId); // fallback to use linkId for grid columns
      if (isQuestionAnswered(answer)) {
        answerList.push(createAnswer(question, answer));
      }
    }

    return true;
  });

  return answerList;
}

function findExtension(extensionArray, url) {
  let result;
  if (extensionArray !== undefined) {
    extensionArray.every((extension) => {
      if (url === extension.url) {
        result = extension.valueCode;
        return false;
      }
      return true;
    });
  }
  return result;
}

function createQuestionnaireResponseType(questionnaireType) {
  let questionnaireResponseTypeCode = 'UNKNOWN';
  if (questionnaireType !== undefined) {
    if (questionnaireType.toLowerCase() === 'GeneralQuestionnaire'.toLowerCase()) {
      questionnaireResponseTypeCode = 'general-questionnaire-response';
    } else if (questionnaireType.toLowerCase() === 'Instrument'.toLowerCase()) {
      questionnaireResponseTypeCode = 'instrument-response';
    } else if (questionnaireType.toLowerCase() === 'SatisfactionSurvey'.toLowerCase()) {
      questionnaireResponseTypeCode = 'satisfactions-survey-response';
    } else if (questionnaireType.toLowerCase() === 'MixtureCatItemBank'.toLowerCase()) {
      questionnaireResponseTypeCode = 'mixture-cat-item-bank-response';
    }
  }
  return questionnaireResponseTypeCode;
}

function createExtension(fhirQuestionnaire) {
  let extensionArray = [];

  let questionnaireType = findExtension(fhirQuestionnaire.extension, 'questionnaire-type');
  extensionArray.push({
    url: 'questionnaire-response-type',
    valueCode: createQuestionnaireResponseType(questionnaireType),
  });
  extensionArray.push(
    {
      url: 'questionnaire-name',
      valueString: fhirQuestionnaire.name,
    },
    {
      url: 'questionnaire-title',
      valueString: fhirQuestionnaire.title,
    },
  );

  return extensionArray;
}

function createQuestionnaireResponse(
  fhirQuestionnaire,
  apiQuestionnaireResponse,
  answers,
  status,
  questionnaireStartTime,
) {
  const d = new Date();
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  const localISOString = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;

  let fhirQuestionnaireResponse = {
    resourceType: 'QuestionnaireResponse',
    identifier: createIdentity(apiQuestionnaireResponse),
    questionnaire: `Questionnaire/${fhirQuestionnaire.id}`,
    status: createStatus(status),
    authored: localISOString,
    extension: createExtension(fhirQuestionnaire),
    item: createAnswers(fhirQuestionnaire.item, answers),
  };

  if (apiQuestionnaireResponse.id !== undefined) {
    fhirQuestionnaireResponse.id = apiQuestionnaireResponse.id;
  }

  let startTimeForScoring = Date.now();
  let decoratedFhirQuestionnaireResponse = decorateWithScores(fhirQuestionnaire, fhirQuestionnaireResponse);
  let finalFhirQuestionnaireResponse = decorateWithHtmlReport(
    fhirQuestionnaire,
    decoratedFhirQuestionnaireResponse,
    answers,
  );
  let endTimeForScoring = Date.now();
  console.log('Time taken: ' + (endTimeForScoring - startTimeForScoring) + ' ms');
  console.log('=====decoratedFhirQuestionnaireResponse====', decoratedFhirQuestionnaireResponse);
  console.log('=====finalFhirQuestionnaireResponse====', finalFhirQuestionnaireResponse);

  return finalFhirQuestionnaireResponse;
}

function buildDemographicByParameter(firstName, lastName, email, phn, gender, birthdate) {
  let demographicArray = [];
  if (firstName !== undefined) {
    demographicArray.push({
      id: 'firstName',
      label: 'First Name: ',
      value: firstName,
    });
  }

  if (lastName !== undefined) {
    demographicArray.push({
      id: 'lastName',
      label: 'Last Name: ',
      value: lastName,
    });
  }

  if (email !== undefined) {
    demographicArray.push({
      id: 'email',
      label: 'Email: ',
      value: email,
    });
  }

  if (phn !== undefined) {
    demographicArray.push({
      id: 'phn',
      label: 'PHN: ',
      value: phn,
    });
  }

  if (gender !== undefined) {
    demographicArray.push({
      id: 'gender',
      label: 'Gender: ',
      value: gender,
    });
  }

  if (birthdate !== undefined) {
    demographicArray.push({
      id: 'birthdate',
      label: 'Birthdate :',
      value: birthdate,
    });
  }

  return demographicArray;
}

function buildDemographicFromFhir(fhir) {
  if (fhir === undefined) {
    return [];
  }

  if (fhir.resourceType !== 'Person' && fhir.resourceType !== 'Patient') {
    return [];
  }

  let firstName;
  let lastName;
  let email;
  let phn;
  let gender;
  let birthdate;
  if (fhir.name !== undefined) {
    fhir.name.forEach((nameRecord) => {
      if (nameRecord.family !== undefined && lastName === undefined) {
        lastName = nameRecord.family;
      }
      if (nameRecord.given !== undefined && firstName === undefined) {
        firstName = nameRecord.given.pop();
      }
    });
  }

  if (fhir.telecom !== undefined) {
    fhir.telecom.forEach((telecom) => {
      if (telecom.system === 'email') {
        if (telecom.value !== undefined && email == undefined) {
          email = telecom.value;
        }
      }
    });
  }

  gender = fhir.gender;
  birthdate = fhir.birthDate;

  return buildDemographicByParameter(firstName, lastName, email, phn, gender, birthdate);
}

export { createQuestionnaireResponse, buildDemographicByParameter, buildDemographicFromFhir };
