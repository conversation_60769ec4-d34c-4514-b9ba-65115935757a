export const MULTIPLE_GRID_QUESTIONNAIRE = {
  resourceType: 'Questionnaire',
  date: '2025-05-23T07:40:54.171Z',
  name: 'TestGrids',
  title: 'TestGrids',
  description: null,
  subjectType: 'Patient',
  extension: [
    {
      url: 'display-dial',
      valueBoolean: false,
    },
    {
      url: 'display-description',
      valueBoolean: true,
    },
    {
      url: 'display-large-buttons',
      valueBoolean: false,
    },
    {
      url: 'display-progress-bar',
      valueBoolean: true,
    },
    {
      url: 'display-score',
      valueBoolean: false,
    },
    {
      url: 'display-score-category',
      valueBoolean: false,
    },
    {
      url: 'display-title',
      valueBoolean: true,
    },
    {
      url: 'questionnaire-type',
      valueCode: 'Instrument',
    },
    {
      url: 'question-unit-per-page',
      valueBoolean: true,
    },
    {
      url: 'trendable',
      valueBoolean: false,
    },
    {
      url: 'pdftemplate-id',
      valueString: '',
    },
    {
      url: 'question-identifier-prefix',
      valueString: 'Item',
    },
    {
      url: 'codeBookHtmlData',
      valueString:
        '\n<h2 style="text-align: center;"><strong>General Data Format</strong></h2>\n<p>The data export file for questionnaire responses has the following characteristics:</p>\n<ol>\n<li data-list-text="1.">\n<p>The file format is “csv”(machine readable)</p>\n</li>\n<li data-list-text="2.">\n<p>One row for each questionnaire response (Long Format)</p>\n</li>\n<li data-list-text="3.">\n<p>Each row contains the following:</p>\n<ol style="list-style-type: lower-alpha;">\n<li data-list-text="a.">\n<p>An identifier for the questionnaire</p>\n</li>\n<li data-list-text="b.">\n<p>The group id of the group the participant is a member of</p>\n</li>\n<li data-list-text="c.">\n<p>The participant id of the user filling in the response</p>\n</li>\n<li data-list-text="d.">\n<p>The start date (YYYY-MM-DD hh:mm:ss) when the participant begins to work on the response</p>\n</li>\n<li data-list-text="e.">\n<p>The completion date (YYYY-MM-DD hh:mm:ss)</p>\n</li>\n<li data-list-text="f.">\n<p>Time spent in completing questionnaire (in seconds)</p>\n</li>\n<li data-list-text="g.">\n<p>The response for each item (Note: For multiple choice questions that allow multiple responses, each allowable response is turned into a item with the possible response of Yes or No. Free form text responses are put in double quotes (e.g.,”I experienced some pain”) so commas can be used inside the response value. Double quotes are escaped with another double quote (e.g.,”I experienced some “”phantom”” pain”). A skipped item will have the corresponding item response set to -99.)</p>\n</li>\n<li data-list-text="h.">\n<p>The computed scores if applicable (Note: if a score cannot be computed because of missing data, the corresponding field will be set to -99)</p>\n</li>\n</ol>\n</li>\n</ol><h1 style="text-align: center;">{Questionnaire.title}</h1>\n<h3>Questionnaire and Item mapping</h3>\n<p>Each questionnaire is given a unique numerical identifier and each item within a questionnaire is given a name that is used to define the columns used in the data export file.</p>\n<table style="border-collapse: collapse; width: 900px;">\n<tbody>\n<tr>\n<td style="width: 714px; border-style: solid; padding-left: 10px;">\n<p>{Questionnaire.title}</p>\n</td>\n<td style="width: 178px; border-style: solid; padding-left: 10px;">\n<p>{Questionnaire.id}</p>\n</td>\n</tr>\n</tbody>\n</table>\n<p> </p>\n{Questionnaire.mappedQuestionsList}\n<p> </p>\n<p>Each computed score within a questionnaire is given a name that is used to define the columns used in the data export file.</p>\n<table style="border-collapse: collapse; width: 900.359px;">\n<tbody>\n<tr>\n<td style="width: 714px; border-style: solid; padding-left: 10px;">\n<p>score</p>\n</td>\n<td style="width: 178px; border-style: solid; padding-left: 10px;">\n<p>S1</p>\n</td>\n</tr>\n</tbody>\n</table>\n<p> </p>\n<p>With the information above, columns that will be presented in an entry for {Questionnaire.title} are defined as:</p>\n<p>{Questionnaire.questionnaireColumns}</p>\n<p> </p>\n<h3>Item response mapping</h3>\n<p>Allowed responses for each item are shown below:</p>\n<p> </p>\n{Questionnaire.mappedResponseList}\n<p> </p>\n<h3>Sample Data</h3>\n<p>k45e7b06-1295-47f2-9577-d8e4d43c5333,Item1, Item2, Item3, S1</p>\n<p>m5187b06-8321-88i2-2342-h456w234l231,Item1, Item2, Item3, Item4, Item5, Item6, S1</p>\n',
    },
    {
      url: 'question-identifier-next-sequence',
      valueInteger: 8,
    },
    {
      url: 'htmltemplate-base64',
      valueString:
        '<h1>{Questionnaire.title}</h1>\n<p>Date: {QuestionnaireResponse.completionDate:format(YYYY-MM-DD HH:mm:ss)}</p>\n<p>Here is a list of items and responses:</p>\n{QuestionnaireResponse.itemsAndResponses}\n<p style="color:gray; font-size:11px;">{Questionnaire.description}</p>',
    },
    {
      url: 'pdftemplate-name',
      valueString: '',
    },
    {
      url: 'list-of-score-definitions',
      extension: [
        {
          url: 'score-id',
          valueCode: '5fcd0bab-4a8e-46f7-b433-0c884a175bf8',
        },
        {
          url: 'score-sequence',
          valueInteger: 0,
        },
        {
          url: 'score-name',
          valueString: 'NewVariable',
        },
        {
          url: 'list-of-formula-definitions',
          extension: [
            {
              extension: [
                {
                  url: 'formula-name',
                  valueString: 'NewVariable-F1',
                },
                {
                  url: 'mathematical-expression',
                  valueString: '',
                },
                {
                  url: 'selection-rule',
                  valueString: 'Select Rule',
                },
              ],
              url: 'set-of-api-formula',
            },
          ],
        },
      ],
    },
  ],
  identifier: [
    {
      use: 'old',
      system: 'questionnaire/identifier',
      value: 'd3ea065c-2d4f-4d38-b4c7-fc4b28d21f78',
      period: {
        start: '2023-12-13T14:06:06+00:00',
        end: '2023-12-13T14:15:33+00:00',
      },
    },
    {
      use: 'usual',
      system: 'urn:uuid',
      value: 'f6356947-59d8-495b-abf7-47973d3a1968',
      period: {
        start: '2023-12-13T14:06:06+00:00',
      },
    },
  ],
  item: [
    {
      linkId: 'Group1',
      item: [
        {
          id: 'complex-qCdYhaGHhNiMYwcik8uTbX',
          linkId: 'Item1',
          type: 'group',
          text: 'Data Grid 1',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5529,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          item: [
            {
              id: '65J2XhkW6xajmokKjXteGH',
              type: 'group',
              item: [
                {
                  id: 0,
                  linkId: 'a6x3os5fRco1XZzkKXLHwe',
                  text: 'Text',
                  type: 'text',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                  ],
                },
                {
                  id: 1,
                  linkId: '2QgByCgXNZqeENFCgWoBsu',
                  text: 'Numeric',
                  type: 'decimal',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 2,
                    },
                  ],
                },
                {
                  id: 2,
                  linkId: 'keEHPM1XwPbNdoyraK3GcV',
                  text: 'Date',
                  type: 'dateTime',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 3,
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          id: '2MrmNM6R6HsPk5PF3VbH3S',
          linkId: 'Item2',
          type: 'text',
          text: 'Enter name',
          item: [
            {
              id: 'vNmmvECF5VQvUNKaqCUsTn',
              type: 'group',
              item: [
                {
                  id: 0,
                  linkId: 'crLi8twdx6LQC55Je2N5BC',
                  text: 'Enter text for grid 1',
                  type: 'text',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                  ],
                },
              ],
            },
          ],
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5520,
            },
            {
              url: 'Item/min-length',
              valueInteger: 0,
            },
            {
              url: 'Item/max-length',
              valueInteger: 255,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
        },
      ],
      id: 'group-46M5bdnjfkff94V788WyrR',
      type: 'group',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 1,
        },
      ],
    },
    {
      linkId: 'Group2',
      item: [
        {
          id: 'oNUnf4gMXoaXdxY26FFuZT',
          linkId: 'Item3',
          type: 'choice',
          text: 'Check2',
          answerOption: [
            {
              valueCoding: {
                id: 0,
                sequence: 1,
                display: '11',
                code: 1,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
              },
            },
            {
              valueCoding: {
                id: 1,
                sequence: 2,
                display: '22',
                code: 2,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
              },
            },
            {
              valueCoding: {
                id: 2,
                sequence: 3,
                display: '33',
                code: 3,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 3,
                  },
                ],
              },
            },
          ],
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 2,
            },
          ],
        },
        {
          id: 'complex-wUSfkqnSHGnSz3t83e6se2',
          linkId: 'Item4',
          type: 'group',
          text: 'Grid2',
          item: [
            {
              id: 'u9TT1YxF1dWADHxqtLsggm',
              type: 'group',
              item: [
                {
                  id: 0,
                  linkId: 'r6nK7FKpdkUqMgn9iV7tMo',
                  text: 'Name',
                  type: 'text',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                  ],
                },
                {
                  id: 1,
                  linkId: 'jj4xyPTXePcCYkxYKJFri3',
                  text: 'Age',
                  type: 'decimal',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 2,
                    },
                  ],
                },
                {
                  id: 2,
                  linkId: '7oQPFkV9juD24t99XorbaE',
                  text: 'text',
                  type: 'text',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 3,
                    },
                  ],
                },
              ],
            },
          ],
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5529,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 2,
            },
          ],
        },
      ],
      id: 'group-maV3SNzumd5PbDHJvaAWSw',
      type: 'group',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 2,
        },
      ],
    },
    {
      linkId: 'Group3',
      item: [
        {
          id: '1EQarZs64wBy3PeB5Ygo5g',
          linkId: 'Item5',
          type: 'choice',
          text: 'Check all that apply',
          answerOption: [
            {
              valueCoding: {
                id: 0,
                sequence: 1,
                display: 'Option 1',
                code: 1,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
              },
            },
            {
              valueCoding: {
                id: 1,
                sequence: 2,
                display: 'Option2 ',
                code: 2,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
              },
            },
            {
              valueCoding: {
                id: 2,
                sequence: 3,
                display: 'Option 3',
                code: 3,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 3,
                  },
                ],
              },
            },
          ],
          item: [
            {
              id: '4KaAuprTVrFmBvFzY6LvPi',
              type: 'group',
              item: [
                {
                  id: 0,
                  linkId: 'gRJj292vAxqz5KZQJYXiEa',
                  text: 'Enter  text for grid3',
                  type: 'dateTime',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                  ],
                },
              ],
            },
          ],
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5514,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: true,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 3,
            },
          ],
        },
        {
          id: '66cPix7w4r2RAR6dM3Je7g',
          linkId: 'Item6',
          type: 'choice',
          text: 'Radio button?',
          answerOption: [
            {
              valueCoding: {
                id: 0,
                sequence: 1,
                display: 'Yes',
                code: 1,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
              },
            },
            {
              valueCoding: {
                id: 1,
                sequence: 2,
                display: 'No',
                code: 2,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
              },
            },
          ],
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 3,
            },
          ],
        },
      ],
      id: 'group-eUm7n176a6Cri24xiNgc4F',
      type: 'group',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 3,
        },
      ],
    },
    {
      linkId: 'Group4',
      item: [
        {
          id: 'complex-sB8NQvv8qCBqacsLMVNVVd',
          linkId: 'Item7',
          type: 'group',
          text: 'Last Grid',
          item: [
            {
              id: 'nDPvF7Ub2Hptyqzbo1qnHa',
              type: 'group',
              item: [
                {
                  id: 0,
                  linkId: 'nzLjj8sGaUa4istJL9YP26',
                  text: 'Treatment Name',
                  type: 'text',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                  ],
                },
                {
                  id: 1,
                  linkId: 't2zUrrSVSA95nxMVAh3e2o',
                  text: 'Age received',
                  type: 'decimal',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 2,
                    },
                  ],
                },
              ],
            },
          ],
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5529,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 4,
            },
          ],
        },
      ],
      id: 'group-bJRemQCF8Eov2hdRSv3Teh',
      type: 'group',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 4,
        },
      ],
    },
  ],
  publisher: 'App-Scoop',
  status: 'draft',
  id: '93a40583-768d-4f0a-938d-f8fdb5e20b56',
  url: 'Questionnaire/93a40583-768d-4f0a-938d-f8fdb5e20b56',
};
