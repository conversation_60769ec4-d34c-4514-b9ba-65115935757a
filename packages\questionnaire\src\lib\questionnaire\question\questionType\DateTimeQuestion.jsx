import React from 'react';
import { TextField } from '@mui/material';
import { QuestionText } from '../QuestionText';
import { Explanation } from '../Explanation';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';

function DateTimeQuestion(props) {
  const { question, handleQuestionResponse } = props;
  const isReadOnly = handleQuestionResponse === undefined;

  const extractExistingAnswer = () => {
    let answer = '';
    if (typeof question.answer === 'object' && !Array.isArray(question.answer) && question.answer !== null) {
      answer = question.answer.valueDateTime;
    }
    return answer;
  };

  const parseExistingAnswer = (dateTimeString) => {
    if (!dateTimeString) return null;

    // Handle both our new format and legacy ISO strings
    if (dateTimeString.includes('T')) {
      const [datePart, timePart] = dateTimeString.split('T');
      const [year, month, day] = datePart.split('-').map(Number);
      const [time] = timePart.split('.'); // Remove milliseconds and timezone if present
      const [hours, minutes, seconds = 0] = time.split(':').map(Number);

      // Create Date object using local components (no timezone conversion)
      return new Date(year, month - 1, day, hours, minutes, seconds);
    }

    // Fallback for other formats
    return new Date(dateTimeString);
  };

  const [value, setValue] = React.useState(parseExistingAnswer(extractExistingAnswer()));

  const handleChange = (newValue) => {
    if (!isReadOnly) {
      setValue(newValue);
      if (newValue instanceof Date && !isNaN(newValue)) {
        // Store the date/time without timezone conversion to preserve the selected values
        const year = newValue.getFullYear();
        const month = String(newValue.getMonth() + 1).padStart(2, '0');
        const day = String(newValue.getDate()).padStart(2, '0');
        const hours = String(newValue.getHours()).padStart(2, '0');
        const minutes = String(newValue.getMinutes()).padStart(2, '0');
        const seconds = String(newValue.getSeconds()).padStart(2, '0');

        // Create ISO-like string but without timezone conversion
        const localISOString = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;

        question.answer = {
          valueDateTime: localISOString,
        };
        handleQuestionResponse(question);
      } else if (newValue === null) {
        question.answer = {
          valueDateTime: null,
        };
        handleQuestionResponse(question);
      }
    }
  };

  return (
    <>
      <QuestionText
        isRequired={question.question.required}
        question={question.question.text}
        extension={question.question.extension}
      />

      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <DateTimePicker
          clearable
          value={value}
          format="yyyy-MM-dd hh:mm a"
          onChange={(newValue) => {
            handleChange(newValue);
          }}
          disableOpenPicker={isReadOnly}
          slots={{
            textField: TextField,
          }}
          slotProps={{
            textField: {
              size: 'small',
              InputProps: {
                placeholder: isReadOnly ? '' : 'YYYY-MM-DD hh:mm aa',
              },
            },
          }}
        />
      </LocalizationProvider>
      <Explanation question={question} />
    </>
  );
}

export default DateTimeQuestion;
