{"name": "@cambianrepo/questionnaire", "publishConfig": {"registry": "https://npm.pkg.github.com/cambianrepo"}, "version": "0.0.18", "type": "module", "engines": {"node": ">=20.0.0", "npm": "please use YARN", "yarn": ">= 1.22.18"}, "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "rollup": "rollup -c", "format": "prettier --write \"./src/**/*.{js,jsx,css,md,json}\" --config ./.prettierrc"}, "dependencies": {"@mui/x-data-grid": "^7.2.0", "@mui/x-date-pickers": "^7.2.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "pdfobject": "^2.3.0"}, "peerDependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.15", "@mui/material": "^5.15.15", "date-fns-tz": "^3.1.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-to-print": "^2.15.1"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "rollup-plugin-postcss": "^4.0.2", "vite": "^5.2.0"}, "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "files": ["dist"]}