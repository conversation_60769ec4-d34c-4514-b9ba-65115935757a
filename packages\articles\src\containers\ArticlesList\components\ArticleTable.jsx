import React, { useState, useMemo, useCallback, useRef } from 'react';
import { Box, TextField, FormControlLabel, Checkbox, Typography } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { format } from 'date-fns';
import { dateFormats, publishedStatuses } from '../../CommonConstants';
import { CustomModal } from '../../../components/CustomModal';
import LaunchIcon from '@mui/icons-material/Launch';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import SaveIcon from '@mui/icons-material/Save';
import DeleteIcon from '@mui/icons-material/Delete';
import { PublishedWithChanges, Close } from '@mui/icons-material';
import { MenuList, Loader } from '@/components';
import { strings } from '../../../utility/strings';

const getDataGridColumns = (handleClick) => [
  {
    field: 'title',
    headerName: 'Name',
    minWidth: 350,
    flex: 1,
    sortable: true,
    hideable: false,
    renderCell: (params) => {
      const { row } = params;
      return row.title;
    },
  },
  {
    field: 'createdDate',
    headerName: 'Created',
    width: 150,
    sortable: true,
    renderCell: (params) => format(new Date(params.value), dateFormats.year_month_day),
  },
  {
    field: 'modifiedDate',
    headerName: 'Modified',
    width: 150,
    sortable: true,
    renderCell: (params) => format(new Date(params.value), dateFormats.year_month_day),
  },
  {
    field: 'publishStatus',
    headerName: 'Published',
    width: 150,
    sortable: true,
    renderCell: (params) => params.value.toUpperCase(),
  },
  {
    field: 'Action',
    headerName: '',
    width: 80,
    sortable: false,
    disableColumnMenu: true,
    hideable: false,
    renderCell: (params) => (
      <MoreVertIcon
        data-articleid={params.row.id}
        data-publishstatus={params.row.publishStatus}
        onClick={handleClick}
        sx={{ cursor: 'pointer', display: 'block', color: 'rgba(0, 0, 0, 0.54)' }}
      />
    ),
  },
];

export const ArticleTable = (props) => {
  const {
    articleList = [],
    handleEditArticle,
    handleDuplicateArticle,
    handleDeleteArticle,
    handleExportArticle,
    handlePublishArticle,
  } = props;

  // Get DataGrid settings from localStorage
  const getDataGridStateFromStorage = () => {
    try {
      const storedState = localStorage.getItem('articleDataGridState');
      return storedState ? JSON.parse(storedState) : {};
    } catch {
      return {};
    }
  };

  const searchFieldRef = useRef(null);
  const [searchText, setSearchText] = useState('');
  const [dataGridState, setDataGridState] = useState(getDataGridStateFromStorage());
  const [anchorEl, setAnchorEl] = useState(null);
  const [openPublishModal, setOpenPublishModal] = useState(false);
  const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [publishStatus, setPublishStatus] = useState({ isPrivate: false, isPublic: false });
  const [selectedArticleId, setSelectedArticleId] = useState('');
  const [publishedStatus, setPublishedStatus] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Save DataGrid state to localStorage
  const saveDataGridState = useCallback((newState) => {
    setDataGridState(newState);
    localStorage.setItem('articleDataGridState', JSON.stringify(newState));
  }, []);

  const handlePublishModal = (articleId, publishRepository) => {
    setOpenPublishModal(true);
    setSelectedArticleId(articleId);
    if (publishRepository === 'private') {
      setPublishStatus({ isPrivate: true, isPublic: false });
    } else if (publishRepository === 'public') {
      setPublishStatus({ isPrivate: false, isPublic: true });
    } else if (publishRepository === 'both') {
      setPublishStatus({ isPrivate: true, isPublic: true });
    } else {
      setPublishStatus({ isPrivate: false, isPublic: false });
    }
  };

  const handleDeleteModal = (articleId, publishStatus) => {
    setOpenDeleteConfirmation(true);
    setSelectedArticleId(articleId);
    setPublishedStatus(publishStatus);
  };

  const handleClick = (event) => {
    const articleId = event.currentTarget.getAttribute('data-articleid');
    const publishStatus = event.currentTarget.getAttribute('data-publishstatus');
    setAnchorEl(event.currentTarget);
    setSelectedArticleId(articleId);
    setPublishedStatus(publishStatus);
  };

  const rows = useMemo(() => {
    if (!articleList?.length) return [];

    return articleList.map((article) => ({
      id: article?.artifactId,
      title: article?.title,
      modifiedDate: article?.modifiedDate,
      createdDate: article?.createdDate || article?.modifiedDate, // Fallback to modifiedDate if createdDate not available
      publishStatus: article?.publishStatus,
    }));
  }, [articleList]);

  const filteredRows = useMemo(() => {
    if (!searchText) return rows;

    const searchLower = searchText.toLowerCase();
    return rows.filter(
      (row) =>
        row.title.toLowerCase().includes(searchLower) || (row.name && row.name.toLowerCase().includes(searchLower)),
    );
  }, [rows, searchText]);

  const handleArticleSearch = useCallback((e) => {
    const searchString = e?.target?.value || '';
    setSearchText(searchString);
  }, []);

  const handleClearSearchKey = useCallback(() => {
    setSearchText('');
    if (searchFieldRef.current) {
      searchFieldRef.current.value = '';
    }
  }, []);

  const handlePublishConfirm = async () => {
    setIsLoading(true);
    try {
      let status = publishedStatuses.no;
      if (publishStatus.isPrivate && !publishStatus.isPublic) {
        status = publishedStatuses.private;
      } else if (!publishStatus.isPrivate && publishStatus.isPublic) {
        status = publishedStatuses.public;
      } else if (publishStatus.isPrivate && publishStatus.isPublic) {
        status = publishedStatuses.both;
      } else if (!publishStatus.isPrivate && !publishStatus.isPublic) {
        status = publishedStatuses.no;
      }
      await handlePublishArticle(selectedArticleId, status);
      setOpenPublishModal(false);
    } catch (error) {
      console.error('Error publishing article:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteConfirm = async () => {
    setIsLoading(true);
    try {
      await handleDeleteArticle(selectedArticleId, publishedStatus);
      setOpenDeleteConfirmation(false);
    } catch (error) {
      console.error('Error deleting article:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const columns = useMemo(
    () =>
      getDataGridColumns(
        handleClick,
        handlePublishModal,
        handleDeleteModal,
        handleEditArticle,
        handleDuplicateArticle,
        handleExportArticle,
      ),
    [
      handleClick,
      handlePublishModal,
      handleDeleteModal,
      handleEditArticle,
      handleDuplicateArticle,
      handleExportArticle,
    ],
  );

  const getMenuItems = (articleId, publishStatus) => [
    {
      id: 'edit',
      label: strings.edit,
      icon: <LaunchIcon sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: async () => {
        setIsLoading(true);
        try {
          await handleEditArticle(articleId, publishStatus);
        } catch (error) {
          console.error('Error editing article:', error);
        } finally {
          setIsLoading(false);
        }
      },
      show: true,
    },
    {
      id: 'duplicate',
      label: strings.duplicate,
      icon: <FileCopyIcon sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: async () => {
        setIsLoading(true);
        try {
          await handleDuplicateArticle(articleId, publishStatus);
        } catch (error) {
          console.error('Error duplicating article:', error);
        } finally {
          setIsLoading(false);
        }
      },
      show: true,
    },
    {
      id: 'export',
      label: strings.export,
      icon: <SaveIcon sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: async () => {
        setIsLoading(true);
        try {
          await handleExportArticle(articleId, publishStatus);
        } catch (error) {
          console.error('Error exporting article:', error);
        } finally {
          setIsLoading(false);
        }
      },
      show: true,
    },
    {
      id: 'publish',
      label: strings.publish,
      icon: <PublishedWithChanges sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: () => handlePublishModal(articleId, publishedStatus),
      show: true,
    },
    {
      id: 'delete',
      label: strings.delete,
      icon: <DeleteIcon sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: () => handleDeleteModal(articleId, publishStatus),
      show: true,
    },
  ];

  const publishModalContent = (
    <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
      <FormControlLabel
        control={
          <Checkbox
            checked={publishStatus.isPrivate}
            onChange={(e) => setPublishStatus({ ...publishStatus, isPrivate: e.target.checked })}
          />
        }
        label={strings.private}
        sx={{ mr: 4 }}
      />
      <FormControlLabel
        control={
          <Checkbox
            checked={publishStatus.isPublic}
            onChange={(e) => setPublishStatus({ ...publishStatus, isPublic: e.target.checked })}
          />
        }
        label={strings.public}
      />
    </Box>
  );

  return (
    <>
      <Loader active={isLoading} />
      <Box sx={{ m: 2 }}>
        <Box sx={{ mb: 2 }}>
          <TextField
            autoComplete="off"
            size="small"
            onChange={handleArticleSearch}
            placeholder={strings.search}
            inputRef={searchFieldRef}
            sx={{ width: { md: 250 }, maxWidth: '100%' }}
            InputProps={{
              endAdornment: searchText && (
                <Close onClick={handleClearSearchKey} sx={{ color: 'grey', cursor: 'pointer' }} />
              ),
            }}
          />
        </Box>
        {articleList?.length ? (
          <Box sx={{ width: '100%' }}>
            <DataGrid
              rows={filteredRows}
              columns={columns}
              initialState={{
                sorting: {
                  sortModel: dataGridState.sortModel || [{ field: 'title', sort: 'asc' }],
                },
                columns: {
                  columnVisibilityModel: dataGridState.columnVisibilityModel || {
                    createdDate: false,
                  },
                },
                pagination: {
                  paginationModel: { pageSize: 15 },
                },
              }}
              onSortModelChange={(sortModel) => {
                saveDataGridState({
                  ...dataGridState,
                  sortModel,
                });
              }}
              onColumnVisibilityModelChange={(columnVisibilityModel) => {
                saveDataGridState({
                  ...dataGridState,
                  columnVisibilityModel,
                });
              }}
              disableRowSelectionOnClick
              disableColumnFilter
              disableDensitySelector
              disableCellFocus
              sortingOrder={['asc', 'desc']}
              pageSizeOptions={[10, 15, 25, 50]}
              paginationMode="client"
              disableColumnResize={true}
              sx={{
                borderRadius: 1,
                border: '1px solid rgba(224, 224, 224, 1)',
                '& .MuiDataGrid-columnSeparator': {
                  display: 'none',
                },
                '& .MuiDataGrid-cell': {
                  py: '0.6rem',
                  px: 2,
                  outline: 'none !important',
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '0.9rem',
                  borderRight: '1px solid rgba(224, 224, 224, 1)',
                  borderBottom: 'none',
                },
                '& .MuiDataGrid-columnHeader': {
                  py: '0.6rem',
                  px: 2,
                  outline: 'none !important',
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '0.9rem',
                  fontWeight: '700 !important',
                  borderRight: '1px solid rgba(224, 224, 224, 1)',
                  borderBottom: 'none',
                },
                '& .MuiDataGrid-columnHeaderTitle': {
                  fontWeight: '700 !important',
                },
              }}
            />
          </Box>
        ) : (
          <Typography>No Articles found</Typography>
        )}
        <MenuList
          menuItems={getMenuItems(selectedArticleId, publishedStatus)}
          anchorEl={anchorEl}
          setAnchorEl={setAnchorEl}
        />
        <CustomModal
          open={openPublishModal}
          onClose={() => setOpenPublishModal(false)}
          onConfirm={handlePublishConfirm}
          title={strings.publishConfirmation}
          subTitle={strings.selectRepositoryYouWantToPublishIn}
          content={publishModalContent}
          saveButtonText={strings.publish}
          closeButtonText={strings.cancel}
        />
        <CustomModal
          open={openDeleteConfirmation}
          onClose={() => setOpenDeleteConfirmation(false)}
          onConfirm={handleDeleteConfirm}
          title={strings.deleteArticle}
          subTitle={strings.thisActionCanNotBeUndone}
          saveButtonText={strings.delete}
          closeButtonText={strings.cancel}
        />
      </Box>
    </>
  );
};
