import React, { useEffect } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Checkbox,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
  IconButton,
  Autocomplete,
} from '@mui/material';
import dayjs from 'dayjs';
import PersonIcon from '@mui/icons-material/Person';
import CallIcon from '@mui/icons-material/Call';
import BadgeIcon from '@mui/icons-material/Badge';
import { useTheme } from '@mui/material/styles';
import HomeIcon from '@mui/icons-material/Home';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import AddIcon from '@mui/icons-material/Add';
import Close from '@mui/icons-material/Close';
import StarOutlineIcon from '@mui/icons-material/StarOutline';
import StarIcon from '@mui/icons-material/Star';
import { SaveButtonWithProgress } from '../SaveButtonWithProgress';
import { ContactField } from '../ContactField/ContactField';
import { CambianTooltip } from '../CambianTooltip';

const useStyles = (theme) => ({
  menuIcon: {
    color: '#0000008a',
  },
  icons: {
    color: theme.palette.primary.main,
    fontSize: '20px',
  },
  closeIcon: {
    fontSize: '20px',
  },
  starButton: {
    paddingRight: 0,
  },
});

export function CoordinatorNewClient(props) {
  const {
    submitBtnText,
    saveBtnClicked,
    cancelBtnText,
    emailConfigs,
    handleEmailChange,
    handlePrimaryEmailChange,
    handleAddEmail,
    handleRemoveEmail,
    submissionHandler,
    handleAddressChange,
    addressConfigs,
    handleAddAddress,
    handleRemoveAddress,
    handlePrimaryAddressChange,
    countriesAndProvincesList,
    phoneNumberConfigs,
    handlePhoneNumberChange,
    handlePrimaryPhoneNumberChange,
    handleAddPhoneNumber,
    handleRemovePhoneNumber,
    handleIdChange,
    idConfigs,
    handleAddHealthCareId,
    handleRemoveHealthCareId,
    handlePrimaryHealthCareIdChange,
    healthcareValues,
    handleFormCancellation,
    firstNameConfigs,
    middleNameConfigs,
    lastNameConfigs,
    genderConfigs,
    dateOfBirthConfigs,
    preferredContactMechanismConfigs,
    handlePreferredContactMechanismChange,
    handleFirstNameChange,
    handleMiddleNameChange,
    handleLastNameChange,
    handleDateOfBirthChange,
    handleGenderChange,
    subscribeToNotificationsConfigs,
    handleSubscribeToNotificationsChange,
  } = props;

  const theme = useTheme();
  const styles = useStyles(theme);

  if (!countriesAndProvincesList || countriesAndProvincesList.length === 0) {
    return <div>Error: No Country/Province List Supplied</div>;
  }

  const getLabels = (country) => {
    switch (country) {
      case 'Canada':
        return { provinceLabel: 'Province', postalCodeLabel: 'Postal Code' };
      case 'USA':
        return { provinceLabel: 'State', postalCodeLabel: 'ZIP Code' };
      default:
        return { provinceLabel: 'Province/State', postalCodeLabel: 'Postal/Zip Code' };
    }
  };

  const selectedCountry = addressConfigs.addresses[0]?.country || '';
  const { provinceLabel, postalCodeLabel } = getLabels(selectedCountry);
  const provincesOrStates = countriesAndProvincesList[selectedCountry] || [];

  const handleFormSubmit = () => {
    submissionHandler();
  };

  return (
    <Box>
      <Grid container spacing={4}>
        {/* Column 1: Demographics */}
        {(firstNameConfigs?.allowed ||
          middleNameConfigs?.allowed ||
          lastNameConfigs?.allowed ||
          genderConfigs?.alloweed ||
          dateOfBirthConfigs?.allowed) && (
          <Grid item xs={12} md={4}>
            <Box mb={2}>
              {/* Heading with Icon */}
              <Box display="flex" alignItems="center" mb={1}>
                <PersonIcon sx={{ ...styles.menuIcon }} />
                <Typography variant="h6" ml={1}>
                  Demographics
                </Typography>
              </Box>
              {/* Demographics Fields */}
              <Stack spacing={1}>
                {/* First Name */}
                {firstNameConfigs?.allowed && (
                  <FormControl fullWidth size="small">
                    <Box display="flex" flexDirection="column">
                      <Box display="flex" alignItems="center">
                        <TextField
                          required={firstNameConfigs?.isRequired || false}
                          name="First Name"
                          label="First Name"
                          value={firstNameConfigs?.firstName || ''}
                          error={(firstNameConfigs?.error && !firstNameConfigs?.firstName) || false}
                          onChange={(e) => handleFirstNameChange(e.target.value)}
                        />
                        <Box maxWidth="56px" width="100%"></Box>
                      </Box>
                      {firstNameConfigs?.error && !firstNameConfigs?.firstName && (
                        <FormHelperText error>{firstNameConfigs?.errorText || ''}</FormHelperText>
                      )}
                    </Box>
                  </FormControl>
                )}

                {/* Middle Name */}
                {middleNameConfigs?.allowed && (
                  <FormControl fullWidth size="small">
                    <Box display="flex" flexDirection="column">
                      <Box display="flex" alignItems="center">
                        <TextField
                          name="Middle Name"
                          label="Middle Name"
                          value={middleNameConfigs?.middleName || ''}
                          onChange={(e) => handleMiddleNameChange(e.target.value)}
                          error={(middleNameConfigs?.error && !middleNameConfigs?.middleName) || false}
                          required={middleNameConfigs?.isRequired || false}
                        />
                        <Box maxWidth="56px" width="100%"></Box>
                      </Box>
                      {middleNameConfigs?.error && !middleNameConfigs?.middleName && (
                        <FormHelperText error>{middleNameConfigs?.errorText || ''}</FormHelperText>
                      )}
                    </Box>
                  </FormControl>
                )}

                {/* Last Name */}
                {lastNameConfigs?.allowed && (
                  <FormControl fullWidth size="small">
                    <Box display="flex" flexDirection="column">
                      <Box display="flex" alignItems="center">
                        <TextField
                          required={lastNameConfigs?.isRequired || false}
                          name="Last Name"
                          label="Last Name"
                          value={lastNameConfigs?.lastName || ''}
                          error={(lastNameConfigs?.error && !lastNameConfigs?.lastName) || false}
                          onChange={(e) => handleLastNameChange(e.target.value)}
                        />
                        <Box maxWidth="56px" width="100%"></Box>
                      </Box>
                      {lastNameConfigs?.error && !lastNameConfigs?.lastName && (
                        <FormHelperText error>{lastNameConfigs?.errorText || ''}</FormHelperText>
                      )}
                    </Box>
                  </FormControl>
                )}

                {/* Date of Birth */}
                {dateOfBirthConfigs?.allowed && (
                  <FormControl fullWidth size="small">
                    <Box display="flex" flexDirection="column">
                      <Box display="flex" alignItems="center">
                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                          <DatePicker
                            maxDate={dayjs()}
                            value={
                              dateOfBirthConfigs?.dateOfBirth
                                ? dayjs(dateOfBirthConfigs?.dateOfBirth, 'YYYY-MM-DD')
                                : null
                            }
                            onChange={(e) => {
                              handleDateOfBirthChange(e === null ? null : dayjs(e).format('YYYY-MM-DD'));
                            }}
                            format={'YYYY-MM-DD'}
                            slots={{
                              textField: TextField,
                            }}
                            slotProps={{
                              textField: {
                                error: dateOfBirthConfigs?.error && !dateOfBirthConfigs?.dateOfBirth,
                                required: dateOfBirthConfigs?.isRequired,
                              },
                            }}
                            label="Date of Birth"
                          />
                        </LocalizationProvider>
                        <Box maxWidth="56px" width="100%"></Box>
                      </Box>
                      {dateOfBirthConfigs?.error && !dateOfBirthConfigs?.dateOfBirth && (
                        <FormHelperText error>{dateOfBirthConfigs?.errorText || ''}</FormHelperText>
                      )}
                    </Box>
                  </FormControl>
                )}

                {/* Gender */}
                {genderConfigs?.allowed && (
                  <FormControl fullWidth size="small">
                    <Box display="flex" flexDirection="column">
                      <Box display="flex" alignItems="center">
                        <InputLabel
                          required={genderConfigs?.isRequired || false}
                          error={(genderConfigs?.error && !genderConfigs?.gender) || false}
                        >
                          Gender
                        </InputLabel>
                        <Select
                          required={genderConfigs?.isRequired || false}
                          value={genderConfigs?.gender || ''}
                          onChange={(event) => handleGenderChange(event.target.value)}
                          error={(genderConfigs?.error && !genderConfigs?.gender) || false}
                          label="Gender"
                        >
                          {genderConfigs?.gender && (
                            <MenuItem value="">
                              <em>None</em>
                            </MenuItem>
                          )}
                          <MenuItem value="FEMALE">Female</MenuItem>
                          <MenuItem value="MALE">Male</MenuItem>
                          <MenuItem value="OTHER">Other</MenuItem>
                        </Select>
                        <Box maxWidth="56px" width="100%"></Box>
                      </Box>
                      {genderConfigs?.error && !genderConfigs?.gender && (
                        <FormHelperText error>{genderConfigs?.errorText || ''}</FormHelperText>
                      )}
                    </Box>
                  </FormControl>
                )}

                {/* Identification Section */}
                {idConfigs?.allowed && (
                  <Box>
                    {/* Heading with Icon */}
                    <Box display="flex" alignItems="center" mb={1} pt={2}>
                      <BadgeIcon sx={{ ...styles.menuIcon }} />
                      <Typography variant="h6" ml={1}>
                        Identification
                      </Typography>
                    </Box>
                    {/* Identification Fields */}
                    <Stack spacing={1}>
                      {idConfigs?.isRequired && idConfigs?.healthCareIds.length === 0 ? (
                        <FormHelperText error>At least one ID is required.</FormHelperText>
                      ) : (
                        idConfigs?.healthCareIds.length > 0 &&
                        (idConfigs?.multiple ? idConfigs?.healthCareIds : [idConfigs?.healthCareIds[0]]).map(
                          (healthCareId, index) => (
                            <Stack spacing={1} key={index}>
                              {/* ID Type and Issuer */}
                              <Box display="flex" flexDirection="row">
                                <Box display="flex" sx={{ maxWidth: '500px', width: '100%' }}>
                                  {/* ID Type */}
                                  <FormControl
                                    sx={{ flex: 1, mr: 1 }}
                                    fullWidth
                                    size="small"
                                    error={
                                      (index === 0 && idConfigs?.idTypeError && !healthCareId?.type) ||
                                      ((healthCareId?.issuer || healthCareId?.value) &&
                                        !healthCareId?.type &&
                                        idConfigs?.idTypeError)
                                    }
                                    required={
                                      (index === 0 && idConfigs?.isRequired) ||
                                      healthCareId?.type ||
                                      healthCareId?.issuer ||
                                      healthCareId?.value
                                    }
                                  >
                                    <InputLabel>Type</InputLabel>
                                    <Select
                                      label="Type"
                                      value={healthCareId?.type || ''}
                                      onChange={(val) => handleIdChange(index, 'type', val.target.value)}
                                    >
                                      {healthCareId?.type && (
                                        <MenuItem value="">
                                          <em>None</em>
                                        </MenuItem>
                                      )}
                                      {healthcareValues.map((type, idx) => (
                                        <MenuItem key={idx} value={type?.idType}>
                                          {type?.idType}
                                        </MenuItem>
                                      ))}
                                    </Select>
                                    <FormHelperText error>
                                      {(index === 0 && idConfigs?.idTypeError && !healthCareId?.type) ||
                                      ((healthCareId?.issuer || healthCareId?.value) &&
                                        !healthCareId?.type &&
                                        idConfigs?.idTypeError)
                                        ? idConfigs?.idTypeErrorText || 'Type is required.'
                                        : ''}
                                    </FormHelperText>
                                  </FormControl>

                                  {/* Issuer */}
                                  <FormControl
                                    sx={{ flex: 1 }}
                                    fullWidth
                                    size="small"
                                    error={
                                      (index === 0 && idConfigs?.issuerError && !healthCareId?.issuer) ||
                                      ((healthCareId?.type || healthCareId?.value) &&
                                        !healthCareId?.issuer &&
                                        idConfigs?.issuerError)
                                    }
                                    required={
                                      (index === 0 && idConfigs?.isRequired) ||
                                      healthCareId?.type ||
                                      healthCareId?.issuer ||
                                      healthCareId?.value
                                    }
                                  >
                                    <InputLabel>Issuer</InputLabel>
                                    <Select
                                      label="Issuer"
                                      value={healthCareId?.issuer || ''}
                                      onChange={(val) => handleIdChange(index, 'issuer', val.target.value)}
                                      renderValue={(selected) => {
                                        const selectedIssuer = healthcareValues
                                          .flatMap((item) => item.issuers)
                                          .find((issuer) => issuer.issuer === selected);
                                        return selectedIssuer ? selectedIssuer.issuer : '';
                                      }}
                                    >
                                      {healthCareId?.issuer && (
                                        <MenuItem value="">
                                          <em>None</em>
                                        </MenuItem>
                                      )}
                                      {healthcareValues
                                        .filter((item) => item.idType === healthCareId?.type)
                                        .flatMap((item) =>
                                          item.issuers
                                            .filter((issuer) => issuer.allowed)
                                            .map((issuer, issuerIdx) => (
                                              <MenuItem key={issuerIdx} value={issuer?.issuer}>
                                                {issuer.displayName || issuer.issuer}
                                              </MenuItem>
                                            )),
                                        )}
                                    </Select>
                                    <FormHelperText error>
                                      {(index === 0 && idConfigs?.issuerError && !healthCareId?.issuer) ||
                                      ((healthCareId?.type || healthCareId?.value) &&
                                        !healthCareId?.issuer &&
                                        idConfigs?.issuerError)
                                        ? idConfigs?.issuerErrorText || 'Issuer is required.'
                                        : ''}
                                    </FormHelperText>
                                  </FormControl>
                                </Box>
                                <IconButton
                                  onClick={() => handlePrimaryHealthCareIdChange(index)}
                                  sx={{ ...styles.starButton }}
                                >
                                  {idConfigs?.primaryHealthCareIdIndex === index ? (
                                    <StarIcon sx={{ ...styles.icons }} />
                                  ) : (
                                    <StarOutlineIcon sx={{ ...styles.icons }} />
                                  )}
                                </IconButton>
                                <IconButton
                                  onClick={() => handleRemoveHealthCareId(index)}
                                  sx={{ ...styles.starButton }}
                                >
                                  <Close sx={{ ...styles.closeIcon }} />
                                </IconButton>
                              </Box>

                              {/* ID Value */}
                              <FormControl fullWidth size="small">
                                <Box display="flex" alignItems="center">
                                  <TextField
                                    required={
                                      (index === 0 && idConfigs.isRequired) ||
                                      healthCareId?.type ||
                                      healthCareId?.issuer ||
                                      healthCareId?.value
                                    }
                                    label="Value"
                                    value={healthCareId?.value || ''}
                                    error={
                                      healthCareId?.validationError ||
                                      (index === 0 && idConfigs?.idValueError && !healthCareId?.value) ||
                                      ((healthCareId?.type || healthCareId?.issuer) &&
                                        !healthCareId?.value &&
                                        idConfigs?.idValueError)
                                    }
                                    helperText={
                                      healthCareId?.validationErrorText ||
                                      (index === 0 && idConfigs?.idValueError && !healthCareId?.value) ||
                                      ((healthCareId?.type || healthCareId?.issuer) &&
                                        !healthCareId?.value &&
                                        idConfigs?.idValueError)
                                        ? idConfigs?.idValueErrorText || 'Value is required.'
                                        : ''
                                    }
                                    onChange={(e) => handleIdChange(index, 'value', e.target.value)}
                                  />
                                  <Box maxWidth="56px" width="100%"></Box>
                                </Box>
                              </FormControl>
                            </Stack>
                          ),
                        )
                      )}
                    </Stack>
                    {idConfigs?.issuerRequiredError && (
                      <FormHelperText error>{idConfigs?.issuerRequiredErrorText || ''}</FormHelperText>
                    )}
                    {/* Button to Add Additional Health Care ID */}
                    {(idConfigs?.multiple || idConfigs.healthCareIds.length === 0) && (
                      <Button
                        size="medium"
                        color="primary"
                        onClick={handleAddHealthCareId}
                        startIcon={<AddIcon />}
                        fullWidth
                        sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                      >
                        Add ID
                      </Button>
                    )}
                  </Box>
                )}
              </Stack>
            </Box>
          </Grid>
        )}

        {/* Column 2: Contact */}
        {(emailConfigs?.allowed ||
          phoneNumberConfigs?.allowed ||
          preferredContactMechanismConfigs?.allowed ||
          subscribeToNotificationsConfigs?.allowed) && (
          <Grid item xs={12} md={4}>
            {/* Contact Section */}
            <Box>
              {/* Heading with Icon */}
              <Box display="flex" alignItems="center" mb={1}>
                <CallIcon sx={{ ...styles.menuIcon }} />
                <Typography variant="h6" ml={1}>
                  Contact
                </Typography>
              </Box>
              {/* Contact Fields */}
              {/* Email */}
              {emailConfigs?.allowed && (
                <Box display="flex" flexDirection="column">
                  <Stack spacing={1}>
                    {' '}
                    {emailConfigs?.isRequired && emailConfigs?.emailAddresses.length === 0 ? (
                      <FormHelperText error>At least one email is required.</FormHelperText>
                    ) : (
                      emailConfigs?.emailAddresses.length > 0 &&
                      (emailConfigs?.multiple ? emailConfigs?.emailAddresses : [emailConfigs?.emailAddresses[0]]).map(
                        (email, index) => (
                          <Stack spacing={1} key={index}>
                            <FormControl fullWidth size="small">
                              <Box display="flex" alignItems="center">
                                <ContactField
                                  contact={emailConfigs?.emailAddresses}
                                  index={index}
                                  type="email"
                                  label="Email"
                                  required={index === 0 && emailConfigs?.isRequired}
                                  value={email?.emailAddress || ''}
                                  onChange={(e) => handleEmailChange(e, index)}
                                  onSetPrimary={() => handlePrimaryEmailChange(index)}
                                  onDelete={() => handleRemoveEmail(index)}
                                  isPrimary={emailConfigs?.primaryEmailIndex === index}
                                  isVerified={email?.verified}
                                  error={
                                    emailConfigs?.emailAddresses[index].validationError ||
                                    (index === 0 ? emailConfigs?.error && !email?.emailAddress : false)
                                  }
                                  helperText={
                                    emailConfigs?.emailAddresses[index].validationErrorText ||
                                    (index === 0 ? emailConfigs?.error && !email?.emailAddress : '')
                                  }
                                  allowMultiple={emailConfigs?.multiple}
                                  otpVerificationEnabled={false}
                                  CambianTooltip={CambianTooltip}
                                />
                              </Box>
                            </FormControl>
                          </Stack>
                        ),
                      )
                    )}
                  </Stack>
                  {(emailConfigs?.multiple || emailConfigs?.emailAddresses.length === 0) && (
                    <Button
                      size="medium"
                      color="primary"
                      onClick={handleAddEmail}
                      startIcon={<AddIcon />}
                      fullWidth
                      sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                    >
                      Add Email
                    </Button>
                  )}
                </Box>
              )}

              {/* Phone */}
              {phoneNumberConfigs?.allowed && (
                <Box display="flex" flexDirection="column">
                  <Stack spacing={1} sx={{ mt: 2 }}>
                    {phoneNumberConfigs?.isRequired && phoneNumberConfigs?.phoneNumbers.length === 0 ? (
                      <FormHelperText error>At least one phone is required.</FormHelperText>
                    ) : (
                      phoneNumberConfigs?.phoneNumbers.length > 0 &&
                      (phoneNumberConfigs?.multiple
                        ? phoneNumberConfigs?.phoneNumbers
                        : [phoneNumberConfigs?.phoneNumbers[0]]
                      ).map((phoneNumber, index) => (
                        <Stack key={index}>
                          <FormControl fullWidth size="small">
                            <Box display="flex" alignItems="center">
                              <ContactField
                                contact={phoneNumberConfigs?.phoneNumbers}
                                index={index}
                                type="phone"
                                label="Phone"
                                required={index === 0 && phoneNumberConfigs?.isRequired}
                                value={phoneNumber?.phoneNumber || ''}
                                onChange={(e) => handlePhoneNumberChange(e, index)}
                                onSetPrimary={() => handlePrimaryPhoneNumberChange(index)}
                                onDelete={() => handleRemovePhoneNumber(index)}
                                isPrimary={phoneNumberConfigs?.primaryPhoneNumberIndex === index}
                                isVerified={phoneNumber?.verified}
                                error={
                                  phoneNumberConfigs?.phoneNumbers[index].validationError ||
                                  (index === 0 ? phoneNumberConfigs?.error && !phoneNumber?.phoneNumber : false)
                                }
                                helperText={
                                  phoneNumberConfigs?.phoneNumbers[index].validationErrorText ||
                                  (index === 0 ? phoneNumberConfigs?.error && !phoneNumber?.phoneNumber : '')
                                }
                                allowMultiple={phoneNumberConfigs?.multiple}
                                otpVerificationEnabled={false}
                                CambianTooltip={CambianTooltip}
                              />
                            </Box>
                          </FormControl>
                        </Stack>
                      ))
                    )}
                  </Stack>
                  {(phoneNumberConfigs?.multiple || phoneNumberConfigs?.phoneNumbers.length === 0) && (
                    <Button
                      size="medium"
                      color="primary"
                      onClick={handleAddPhoneNumber}
                      startIcon={<AddIcon />}
                      fullWidth
                      sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                    >
                      Add Phone
                    </Button>
                  )}
                </Box>
              )}
              {preferredContactMechanismConfigs?.allowed && (
                <FormControl fullWidth size="small" sx={{ mt: 2 }}>
                  <Box display="flex" flexDirection="column">
                    <Box display="flex" alignItems="center">
                      <InputLabel
                        required={preferredContactMechanismConfigs?.isRequired || false}
                        error={
                          (preferredContactMechanismConfigs?.error &&
                            !preferredContactMechanismConfigs?.preferredContactMechanism) ||
                          false
                        }
                      >
                        Preferred Contact Method
                      </InputLabel>
                      <Select
                        required={preferredContactMechanismConfigs?.isRequired || false}
                        value={preferredContactMechanismConfigs?.preferredContactMechanism || ''}
                        onChange={(event) => handlePreferredContactMechanismChange(event.target.value)}
                        error={
                          (preferredContactMechanismConfigs?.error &&
                            !preferredContactMechanismConfigs?.preferredContactMechanism) ||
                          false
                        }
                        label="Preferred Contact Method"
                      >
                        {preferredContactMechanismConfigs?.preferredContactMechanism && (
                          <MenuItem value="">
                            <em>None</em>
                          </MenuItem>
                        )}
                        {emailConfigs?.emailAddresses?.some((email) => email?.emailAddress?.trim()) && (
                          <MenuItem value="Email">Email</MenuItem>
                        )}
                        {phoneNumberConfigs?.phoneNumbers?.some((phone) => phone?.phoneNumber?.trim()) && (
                          <MenuItem value="Phone">Phone</MenuItem>
                        )}
                      </Select>
                      <Box maxWidth="56px" width="100%"></Box>
                    </Box>
                    {preferredContactMechanismConfigs?.error &&
                      !preferredContactMechanismConfigs?.preferredContactMechanism && (
                        <FormHelperText error>{preferredContactMechanismConfigs?.errorText || ''}</FormHelperText>
                      )}
                  </Box>
                </FormControl>
              )}
              {/* Subscribe to Notifications Checkbox */}
              {subscribeToNotificationsConfigs?.allowed && (
                <Box mt={2}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={subscribeToNotificationsConfigs?.subscribeToNotifications}
                        onChange={(event) => handleSubscribeToNotificationsChange(event.target.checked)}
                      />
                    }
                    label="Notifications"
                  />
                </Box>
              )}
            </Box>
          </Grid>
        )}

        {/* Column 3: Addresses */}
        {addressConfigs?.allowed && (
          <Grid item xs={12} md={4}>
            <Box mb={2}>
              {/* Heading with Icon */}
              <Box display="flex" alignItems="center" mb={1}>
                <HomeIcon sx={{ ...styles.menuIcon }} />
                <Typography variant="h6" ml={1}>
                  Address
                </Typography>
              </Box>
              {/* Address Fields */}
              {addressConfigs?.isRequired && addressConfigs?.addresses.length === 0 ? (
                <FormHelperText error>At least one address is required.</FormHelperText>
              ) : (
                addressConfigs?.addresses.length > 0 &&
                (addressConfigs?.multiple ? addressConfigs?.addresses : [addressConfigs?.addresses[0]]).map(
                  (address, index) => (
                    <Stack spacing={1} key={index} sx={{ mt: index > 0 ? 4 : 0 }}>
                      {/* Address Line One */}
                      <Box display="flex" flexDirection="column">
                        <FormControl fullWidth size="small">
                          <Box display="flex" alignItems="center">
                            <TextField
                              label={`Line 1`}
                              value={address?.address1 || ''}
                              onChange={(e) => handleAddressChange(index, 'address1', e.target.value)}
                              error={index === 0 ? addressConfigs?.addressOneError && !address?.address1 : false}
                              helperText={index === 0 && !address?.address1 ? addressConfigs.addressOneErrorText : ''}
                              required={(index === 0 && addressConfigs?.isRequired) || false}
                            />
                            <IconButton onClick={() => handlePrimaryAddressChange(index)} sx={{ ...styles.starButton }}>
                              {addressConfigs?.primaryAddressIndex === index ? (
                                <StarIcon sx={{ ...styles.icons }} />
                              ) : (
                                <StarOutlineIcon sx={{ ...styles.icons }} />
                              )}
                            </IconButton>
                            <IconButton onClick={() => handleRemoveAddress(index)} sx={{ ...styles.starButton }}>
                              <Close sx={{ ...styles.closeIcon }} />
                            </IconButton>
                          </Box>
                        </FormControl>
                      </Box>

                      {/* Address Line Two */}
                      <FormControl fullWidth size="small">
                        <Box display="flex" flexDirection="column">
                          <Box display="flex" alignItems="center">
                            <TextField
                              label={`Line 2`}
                              value={address?.address2 || ''}
                              onChange={(e) => handleAddressChange(index, 'address2', e.target.value)}
                              error={index === 0 ? addressConfigs?.addressTwoError && !address?.address2 : false}
                              helperText={index === 0 && !address?.address2 ? addressConfigs.addressTwoErrorText : ''}
                              required={false}
                            />
                            <Box maxWidth="56px" width="100%"></Box>
                          </Box>
                        </Box>
                      </FormControl>

                      {/* City */}
                      <FormControl fullWidth size="small">
                        <Box display="flex" flexDirection="column">
                          <Box display="flex" alignItems="center">
                            <TextField
                              label={`City`}
                              value={address?.city || ''}
                              onChange={(e) => handleAddressChange(index, 'city', e.target.value)}
                              error={index === 0 ? addressConfigs?.cityError && !address?.city : false}
                              helperText={index === 0 && !address?.city ? addressConfigs.cityErrorText : ''}
                              required={(index === 0 && addressConfigs?.isRequired) || false}
                            />
                            <Box maxWidth="56px" width="100%"></Box>
                          </Box>
                        </Box>
                      </FormControl>

                      {/* Country */}
                      <FormControl fullWidth size="small">
                        <Box display="flex" flexDirection="column">
                          <Box display="flex" alignItems="center">
                            <Autocomplete
                              sx={{ maxWidth: '500px', width: '100%' }}
                              value={address?.country || ''}
                              onChange={(event, value) => {
                                handleAddressChange(index, 'country', value || '');
                                if (!value) {
                                  handleAddressChange(index, 'province', '');
                                }
                              }}
                              onInputChange={(event, newInputValue) => {
                                handleAddressChange(index, 'country', newInputValue);
                              }}
                              error={index === 0 ? addressConfigs?.countryError && !address?.country : false}
                              required={(index === 0 && addressConfigs?.isRequired) || false}
                              options={Object.keys(countriesAndProvincesList)}
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  label="Country"
                                  error={index === 0 ? addressConfigs?.countryError && !address?.country : false}
                                  required={(index === 0 && addressConfigs?.isRequired) || false}
                                />
                              )}
                              freeSolo
                              autoSelect={false}
                            />
                            <Box maxWidth="56px" width="100%"></Box>
                          </Box>
                          {index === 0 && addressConfigs?.countryError && !address?.country && (
                            <FormHelperText error>{addressConfigs?.countryErrorText || ''}</FormHelperText>
                          )}
                        </Box>
                      </FormControl>

                      {/* Province */}
                      <FormControl fullWidth size="small">
                        <Box display="flex" flexDirection="column">
                          <Box display="flex" alignItems="center">
                            {provincesOrStates.length > 0 ? (
                              <>
                                <InputLabel
                                  error={index === 0 ? addressConfigs?.provinceError && !address?.province : false}
                                  required={(index === 0 && addressConfigs?.isRequired) || false}
                                >
                                  {provinceLabel}
                                </InputLabel>
                                <Select
                                  value={address?.province || ''}
                                  onChange={(e) => handleAddressChange(index, 'province', e.target.value)}
                                  error={index === 0 ? addressConfigs?.provinceError && !address?.province : false}
                                  required={(index === 0 && addressConfigs?.isRequired) || false}
                                  label={provinceLabel}
                                >
                                  {address?.province && (
                                    <MenuItem value="">
                                      <em>None</em>
                                    </MenuItem>
                                  )}
                                  {provincesOrStates.map((province, idx) => (
                                    <MenuItem key={idx} value={province}>
                                      {province}
                                    </MenuItem>
                                  ))}
                                </Select>
                              </>
                            ) : (
                              <TextField
                                label={provinceLabel}
                                value={address?.province || ''}
                                onChange={(e) => handleAddressChange(index, 'province', e.target.value)}
                                error={index === 0 ? addressConfigs?.provinceError && !address?.province : false}
                                required={(index === 0 && addressConfigs?.isRequired) || false}
                              />
                            )}
                            <Box maxWidth="56px" width="100%"></Box>
                          </Box>
                          {index === 0 && addressConfigs?.provinceError && !address?.province && (
                            <FormHelperText error>{addressConfigs?.provinceErrorText || ''}</FormHelperText>
                          )}
                        </Box>
                      </FormControl>

                      {/* Postal Code */}
                      <FormControl fullWidth size="small">
                        <Box display="flex" flexDirection="column">
                          <Box display="flex" alignItems="center">
                            <TextField
                              label={postalCodeLabel}
                              value={address?.postalCode || ''}
                              onChange={(e) => handleAddressChange(index, 'postalCode', e.target.value)}
                              error={
                                address?.postalCodeValidationError ||
                                (index === 0 ? addressConfigs?.postalCodeError && !address?.postalCode : false)
                              }
                              helperText={
                                address?.postalCodeValidationErrorText ||
                                (index === 0 && !address?.postalCode ? addressConfigs.postalCodeErrorText : '')
                              }
                              required={(index === 0 && addressConfigs?.isRequired) || false}
                            />
                            <Box maxWidth="56px" width="100%"></Box>
                          </Box>
                        </Box>
                      </FormControl>
                    </Stack>
                  ),
                )
              )}
              {/* Button to Add Additional Address */}
              {(addressConfigs?.multiple || addressConfigs?.addresses.length === 0) && (
                <Button
                  size="medium"
                  color="primary"
                  onClick={handleAddAddress}
                  startIcon={<AddIcon />}
                  fullWidth
                  sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                >
                  Add Address
                </Button>
              )}
            </Box>
          </Grid>
        )}
      </Grid>

      {/* Submit and Cancel Buttons */}
      <Grid container justifyContent="flex-end">
        <Stack direction="row" spacing={1}>
          {/* Cancel */}
          <Button variant="outlined" size={'small'} onClick={handleFormCancellation}>
            {cancelBtnText || 'Cancel'}
          </Button>
          {/* Submit */}
          <SaveButtonWithProgress
            saving={saveBtnClicked}
            buttonText={submitBtnText || 'Save'}
            onClickHandler={() => {
              handleFormSubmit();
            }}
          />
        </Stack>
      </Grid>
    </Box>
  );
}

export default CoordinatorNewClient;
