{"name": "@cambianrepo/ui", "publishConfig": {"registry": "https://npm.pkg.github.com/cambianrepo"}, "version": "0.0.64", "scripts": {"rollup": "rollup -c", "start": "react-scripts --max_old_space_size=5120 start", "build": "react-scripts --max_old_space_size=5120 build", "eject": "react-scripts eject", "yalc-publish": "yalc publish"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "peerDependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.15", "@mui/material": "^5.15.15", "@mui/styles": "^5.14.18", "@mui/x-date-pickers": "^7.2.0", "chart.js": "^3.9.1", "chartjs-adapter-moment": "^1.0.0", "pdfobject": "^2.2.8", "react": "^18.2.0", "react-chartjs-2": "^4.0.1", "react-dom": "^18.2.0", "react-phone-input-2": "^2.15.1", "react-qr-code": "^2.0.12"}, "devDependencies": {"@rollup/plugin-replace": "^3.0.0", "amazon-cognito-identity-js": "^6.1.1", "aws-sdk": "^2.1281.0", "chart.js": "^3.9.1", "chartjs-adapter-moment": "^1.0.0", "jwk-to-pem": "^2.0.5", "markdown-to-jsx": "^7.3.2", "moment": "^2.29.4", "node-fetch": "^3.3.0", "pdfobject": "^2.2.8", "react-chartjs-2": "^4.0.1", "react-hook-use-state": "^1.1.0", "react-qr-code": "^2.0.12", "react-scripts": "^5.0.0", "request": "^2.88.2"}, "dependencies": {"compress.js": "^1.2.2", "date-fns": "^3.6.0", "dayjs": "^1.11.9", "libphonenumber-js": "^1.12.9", "mui-markdown": "^1.1.11", "react-hook-form": "^7.56.1", "react-hook-form-mui": "^7.6.0", "react-i18next": "^15.1.1", "react-phone-input-2": "^2.15.1", "rimraf": "^5.0.1", "uuid": "^9.0.0"}, "browser": {"crypto": false}, "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "files": ["dist"]}