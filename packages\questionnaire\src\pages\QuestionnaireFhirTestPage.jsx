import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, Di<PERSON>r, Grid, <PERSON>ack, Typography } from '@mui/material';
import { QuestionnaireV2 as Questionnaire } from '../lib/questionnaire';
import { FHIR_QUESTIONNAIRE_EQ5D5L_WITH_HEADER } from './data/questionnaireFhir/FHIR_QUESTIONNAIRE_EQ5D5L_WITH_HEADER';
import { FHIR_ALL_QUESTION } from './data/questionnaireFhir/FHIR_ALL_QUESTION';
import { FHIR_SIMPLE_GRID_QUESTION } from './data/questionnaireFhir/FHIR_SIMPLE_GRID_QUESTION';
import { FHIR_QUESTIONNAIRE_SAVE_FOR_LATER } from './data/questionnaireFhir/FHIR_QUESTIONNAIRE_SAVE_FOR_LATER';
import { FHIR_QUESTIONNAIRE_RESPONSE_SAVE_FOR_LATER } from './data/questionnaireFhir/FHIR_QUESTIONNAIRE_RESPONSE_SAVE_FOR_LATER';
import { FHIR_SAVE_FOR_LATER_REQUIRED_QUESTIONS } from './data/questionnaireFhir/FHIR_SAVE_FOR_LATER_REQUIRED_QUESTIONS';
import { FHIR_QUESTIONNAIRE_PROBLEMATIC_QUESTIONS } from './data/questionnaireFhir/FHIR_QUESTIONNAIRE_PROBLEMATIC_QUESTIONS';
import { FHIR_GRID_QUESTION } from './data/questionnaireFhir/FHIR_GRID_QUESTION';
import { FHIR_GRID_QUESTION_RESPONSE_SAVE_FOR_LATER } from './data/questionnaireFhir/FHIR_GRID_QUESTION_RESPONSE_SAVE_FOR_LATER';
import { HTML_TEMPLATE_REPORT } from './data/questionnaireFhir/HTML_TEMPLATE_REPORT';
import { FHIR_MULTIPLE_CHOICE_OTHER } from './data/questionnaireFhir/FHIR_MULTIPLE_CHOICE_OTHER';
import { FHIR_NUMERIC_QUESTIONS } from './data/questionnaireFhir/FHIR_NUMERIC_QUESTIONS';
import { bc_cancer, bc_response, cv, cv_response, large_btn } from './data/questionnaireFhir/bc_cancer';
import { ALL_QUESTION_QUESTIONNAIRE } from './data/questionnaireFhir/ALL_QUESTION_QUESTIONNAIRE';
import { MULTIPLE_GRID_QUESTIONNAIRE } from './data/questionnaireFhir/MULTIPLE_GRID_QUESTIONNAIRE';
import { QuestionnaireReportViewer } from '../lib/questionnaire/QuestionnaireReportViewer';

function Empty(props) {
  return <div>This component is empty</div>;
}

function QuestionnaireFhirTestPage() {
  const [component, setComponent] = React.useState(<Empty />);

  const updateComponent = (component) => {
    setComponent(component);
  };

  const [fhirResponse, setFhirResponse] = useState();

  const questionnaireSaveCallback = (
    fhirQuestionnaire,
    fhirQuestionnaireResponse,
    questionMap,
    isComplete,
    isCancelled,
    isSavedForLater,
  ) => {
    console.log('questionnaireSaveCallback: ' + isComplete + ':' + isCancelled + ':' + isSavedForLater);
    if (isCancelled) {
      console.log('Questionnaire Response CANCELLED');
    } else if (isComplete) {
      setFhirResponse(fhirQuestionnaireResponse);
      console.log('Questionnaire Response COMPLETED');
      console.log(fhirQuestionnaireResponse);

      console.log(JSON.stringify(fhirQuestionnaireResponse));
    } else if (isSavedForLater) {
      console.log('Questionnaire Response SAVED FOR LATER');
      console.log(fhirQuestionnaireResponse);

      console.log(JSON.stringify(fhirQuestionnaireResponse));
    }
  };

  const questionnairePageTransitionCallback = (fhirQuestionnaire, fhirQuestionnaireResponse, questionMap) => {
    console.log('questionnaireSavePageTransitionCallback invoked');
    console.log(fhirQuestionnaireResponse);
  };

  return (
    <div style={{ backgroundColor: '#F5F5F5' }}>
      <h1>Questionnaire Page</h1>
      <Grid container direction="row">
        <Box component={Grid} item xs={3} display={{ xs: 'block' }}>
          <Button
            variant="contained"
            onClick={() => {
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2}>
                  <h4>All Question Questionnaire</h4>
                  <Questionnaire
                    fhirQuestionnaire={ALL_QUESTION_QUESTIONNAIRE}
                    // fhirResponse={bc_response}
                    isSaveForLaterActive={true}
                    containedQuestionnaire={true}
                    nextButtonLabelOverride="Next Page"
                    previousButtonLabelOverride="Previous Page"
                    submitButtonLabelOverride="Save"
                    questionnaireCallback={questionnaireSaveCallback}
                  />
                </Stack>,
              );
            }}
          >
            All Question Questionnaire
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2}>
                  <h4>All Question Questionnaire</h4>
                  <Questionnaire
                    fhirQuestionnaire={MULTIPLE_GRID_QUESTIONNAIRE}
                    fhirResponse={bc_response}
                    isSaveForLaterActive={true}
                    containedQuestionnaire={true}
                    nextButtonLabelOverride="Next Page"
                    previousButtonLabelOverride="Previous Page"
                    submitButtonLabelOverride="Save"
                    questionnaireCallback={questionnaireSaveCallback}
                  />
                </Stack>,
              );
            }}
          >
            Multiple Grid Questionnaire
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2}>
                  <h4>Numeric Questionnaire</h4>
                  <Questionnaire
                    fhirQuestionnaire={large_btn}
                    // fhirResponse={bc_response}
                    isSaveForLaterActive={true}
                    containedQuestionnaire={true}
                    nextButtonLabelOverride="Next Page"
                    previousButtonLabelOverride="Previous Page"
                    submitButtonLabelOverride="Save"
                    questionnaireCallback={questionnaireSaveCallback}
                  />
                </Stack>,
              );
            }}
          >
            bc cancer Questionnaire
          </Button>

          <Button
            variant="contained"
            onClick={() => {
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2}>
                  <h4>Numeric Questionnaire</h4>
                  <Questionnaire
                    fhirQuestionnaire={FHIR_NUMERIC_QUESTIONS}
                    isSaveForLaterActive={true}
                    containedQuestionnaire={true}
                    nextButtonLabelOverride="Next Page"
                    previousButtonLabelOverride="Previous Page"
                    submitButtonLabelOverride="Save"
                    questionnaireCallback={questionnaireSaveCallback}
                  />
                </Stack>,
              );
            }}
          >
            Numeric Questionnaire
          </Button>

          <Button
            variant="contained"
            onClick={() => {
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2}>
                  <h4>Other Multiple Choice</h4>
                  <Questionnaire
                    fhirQuestionnaire={FHIR_MULTIPLE_CHOICE_OTHER}
                    isSaveForLaterActive={true}
                    containedQuestionnaire={true}
                    nextButtonLabelOverride="Next Page"
                    previousButtonLabelOverride="Previous Page"
                    submitButtonLabelOverride="Save"
                    questionnaireCallback={questionnaireSaveCallback}
                  />
                </Stack>,
              );
            }}
          >
            Multiple choice other textbox
          </Button>

          <Button
            variant="contained"
            onClick={() => {
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2}>
                  <h4>Questionnaire with no responses</h4>
                  <Questionnaire
                    fhirQuestionnaire={HTML_TEMPLATE_REPORT}
                    isSaveForLaterActive={true}
                    containedQuestionnaire={true}
                    nextButtonLabelOverride="Next Page"
                    previousButtonLabelOverride="Previous Page"
                    submitButtonLabelOverride="Save"
                    questionnaireCallback={questionnaireSaveCallback}
                  />
                </Stack>,
              );
            }}
          >
            HTML Report Template
          </Button>

          <Button
            variant="contained"
            onClick={() => {
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2}>
                  <h4>Questionnaire with no responses</h4>
                  <Questionnaire
                    fhirQuestionnaire={FHIR_QUESTIONNAIRE_EQ5D5L_WITH_HEADER}
                    isSaveForLaterActive={false}
                    nextButtonLabelOverride="Next Page"
                    previousButtonLabelOverride="Previous Page"
                    submitButtonLabelOverride="Save"
                    questionnaireCallback={questionnaireSaveCallback}
                  />
                </Stack>,
              );
            }}
          >
            Simple EQ-5D-5L Questionnaire
          </Button>

          <Button
            variant="contained"
            onClick={() => {
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2}>
                  <h4>Save for Later</h4>
                  <Questionnaire
                    fhirQuestionnaire={FHIR_QUESTIONNAIRE_SAVE_FOR_LATER}
                    fhirResponse={FHIR_QUESTIONNAIRE_RESPONSE_SAVE_FOR_LATER}
                    isSaveForLaterActive={true}
                    questionnaireCallback={questionnaireSaveCallback}
                    containedQuestionnaire={true}
                  />

                  <h4>Multi page Grid Question</h4>
                  <Questionnaire
                    fhirQuestionnaire={FHIR_GRID_QUESTION}
                    fhirResponse={FHIR_GRID_QUESTION_RESPONSE_SAVE_FOR_LATER}
                    isSaveForLaterActive={true}
                    containedQuestionnaire={true}
                    questionnaireCallback={questionnaireSaveCallback}
                  />

                  <Questionnaire
                    fhirQuestionnaire={FHIR_SAVE_FOR_LATER_REQUIRED_QUESTIONS}
                    isSaveForLaterActive={true}
                    isStatusEnabled={false}
                    containedQuestionnaire={true}
                    enableCancelConfirmation={true}
                    questionnaireCallback={questionnaireSaveCallback}
                  />

                  <h4>Page Transition callback</h4>
                  <Questionnaire
                    fhirQuestionnaire={FHIR_QUESTIONNAIRE_PROBLEMATIC_QUESTIONS}
                    isSaveForLaterActive={false}
                    isStatusEnabled={true}
                    containedQuestionnaire={true}
                    pageTransitionCallback={questionnairePageTransitionCallback}
                    questionnaireCallback={questionnaireSaveCallback}
                  />
                </Stack>,
              );
            }}
          >
            Save for Later
          </Button>

          {/* <Button
            variant="contained"
            onClick={() => {
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2}>
                  <h4>Questionnaire with no responses</h4>
                  <Questionnaire
                    fhirQuestionnaire={FHIR_ALL_QUESTION}
                    isSaveForLaterActive={false}
                    containedQuestionnaire={true}
                    questionnaireCallback={questionnaireSaveCallback}
                  />
                </Stack>,
              );
            }}
          >
            All Question Questionnaire
          </Button> */}

          <Button
            variant="contained"
            onClick={() => {
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2}>
                  <h4>Simple Grid Question</h4>
                  <Questionnaire
                    fhirQuestionnaire={FHIR_SIMPLE_GRID_QUESTION}
                    containedQuestionnaire={true}
                    questionnaireCallback={questionnaireSaveCallback}
                  />

                  <h4>Multi page Grid Question</h4>
                  <Questionnaire
                    fhirQuestionnaire={FHIR_GRID_QUESTION}
                    containedQuestionnaire={true}
                    isSaveForLaterActive={false}
                    questionnaireCallback={questionnaireSaveCallback}
                  />
                </Stack>,
              );
            }}
          >
            Grid Questionnaire
          </Button>
        </Box>

        <Box component={Grid} item xs={9} display={{ xs: 'block' }}>
          <div
            style={{
              margin: '25px',
              outline: 'dashed 1px black',
            }}
          >
            {component}
            {fhirResponse && (
              <>
                <Divider />
                <Typography sx={{ mt: 4 }}> Generated questionnaire report</Typography>
                <QuestionnaireReportViewer
                  fhirQuestionnaire={MULTIPLE_GRID_QUESTIONNAIRE}
                  fhirResponse={fhirResponse}
                />
              </>
            )}
          </div>
        </Box>
      </Grid>
    </div>
  );
}

export { QuestionnaireFhirTestPage };
