import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import babel from '@rollup/plugin-babel';
import alias from '@rollup/plugin-alias';
import postcss from 'rollup-plugin-postcss';
import url from '@rollup/plugin-url';
import image from '@rollup/plugin-image';
import path from 'path';
import packageJson from './package.json';

export default {
  input: 'src/containers/index.jsx',
  output: [
    {
      file: packageJson.main,
      format: 'cjs',
      sourcemap: true,
    },
    {
      file: packageJson.module,
      format: 'es',
      sourcemap: true,
    },
  ],
  plugins: [
    alias({
      entries: [{ find: '@', replacement: path.resolve(__dirname, 'src') }],
    }),
    resolve({
      extensions: ['.mjs', '.js', '.jsx', '.json'],
    }),
    babel({
      exclude: 'node_modules/**',
      presets: ['@babel/preset-env', ['@babel/preset-react', { runtime: 'automatic' }]],
      extensions: ['.js', '.jsx'],
    }),
    postcss({
      modules: true,
      extract: true,
      minimize: true,
      use: ['sass'],
    }),
    url(),
    commonjs(),
    image(),
  ],
  external: Object.keys(packageJson.peerDependencies || {}),
};
