import React, { useState, useMemo, useCallback } from 'react';
import { Box, Paper, IconButton, useMediaQuery, useTheme } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { MessageGrid } from './MessageGrid.jsx';
import { MessageDetails } from './MessageDetails.jsx';
import { APPLICATION_CONTEXT_NAVIGATOR } from '../constants.js';

export const MessagingContainer = ({
  type,
  messages,
  onMessageClick,
  selectedMessageId,
  selectedMessage,
  isLoading,
  error,
  sortModel,
  onSortModelChange,
  searchText,
  paginationOptions = { defaultPageSize: 5, pageSizeOptions: [5, 10, 25] },
  onDelete,
  applicationContext = APPLICATION_CONTEXT_NAVIGATOR,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  const isSmallScreen = isMobile || isTablet;

  const [selectedMessageState, setSelectedMessageState] = useState(null);
  const [pageSize, setPageSize] = useState(paginationOptions.defaultPageSize);

  const handleMessageClick = useCallback(
    (message) => {
      setSelectedMessageState(message);
      onMessageClick(message);
    },
    [onMessageClick],
  );

  const handleCloseDetails = useCallback(() => {
    setSelectedMessageState(null);
    onMessageClick(null);
  }, [onMessageClick]);

  const handlePageSizeChange = useCallback((newPageSize) => {
    setPageSize(newPageSize);
  }, []);

  // Find the selected message from the messages array
  const selectedMessageObject = useMemo(() => {
    if (!selectedMessageId || !messages) return null;
    return messages.find((msg) => msg.id === selectedMessageId) || null;
  }, [selectedMessageId, messages]);

  // MessageGrid props
  const messageGridProps = useMemo(
    () => ({
      type,
      messages,
      onMessageClick: handleMessageClick,
      selectedMessageId,
      isLoading,
      error,
      sortModel,
      onSortModelChange,
      searchText,
      pageSize,
      onPageSizeChange: handlePageSizeChange,
      pageSizeOptions: paginationOptions.pageSizeOptions,
      onDelete,
    }),
    [
      type,
      messages,
      handleMessageClick,
      selectedMessageId,
      isLoading,
      error,
      sortModel,
      onSortModelChange,
      searchText,
      pageSize,
      handlePageSizeChange,
      paginationOptions.pageSizeOptions,
      onDelete,
    ],
  );

  // MessageDetails props
  const messageDetailsProps = useMemo(
    () => ({
      message: selectedMessage || selectedMessageObject,
      onClose: handleCloseDetails,
      type,
      applicationContext,
    }),
    [selectedMessage, selectedMessageObject, handleCloseDetails, type, applicationContext],
  );

  // Render the mobile overlay conditionally
  const mobileOverlay = useMemo(() => {
    if (isSmallScreen && (selectedMessageState || selectedMessageObject)) {
      return (
        <Paper
          elevation={3}
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 1,
            overflow: 'auto',
          }}
        >
          <Box sx={{ p: 2 }}>
            <IconButton onClick={handleCloseDetails} sx={{ position: 'absolute', right: 16, top: 16 }}>
              <CloseIcon />
            </IconButton>
            <MessageDetails {...messageDetailsProps} />
          </Box>
        </Paper>
      );
    }
    return null;
  }, [isSmallScreen, selectedMessageState, selectedMessageObject, handleCloseDetails, messageDetailsProps]);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Box
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
        }}
      >
        <Box
          sx={{
            flexGrow: 1,
            mb: { xs: 2, md: 0 },
            width: { xs: '100%', md: '60%' },
            position: 'relative',
          }}
        >
          {mobileOverlay}
          <MessageGrid {...messageGridProps} />
        </Box>

        {/* Desktop MessageDetails panel */}
        {!isSmallScreen && (
          <Box
            sx={{
              width: '40%',
              ml: 2,
              display: { xs: 'none', md: 'block' },
              overflow: 'auto',
            }}
          >
            <MessageDetails {...messageDetailsProps} />
          </Box>
        )}
      </Box>
    </Box>
  );
};
