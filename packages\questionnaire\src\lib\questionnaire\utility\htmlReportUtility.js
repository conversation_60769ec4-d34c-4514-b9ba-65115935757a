import { format } from 'date-fns';
import questionnaireUtility, {
  extractExtension,
  getCalculatedScores,
  getQuestionnaireCompletionDate,
} from './questionnaireUtility';
import { formatInTimeZone } from 'date-fns-tz';

function getDataGridHtml(variable, gridData) {
  // Check if gridData exists and has required properties
  if (!gridData || !gridData.cols || !gridData.rows) {
    return '';
  }
  const hasRowResponses = (row) => {
    return row.some((cell) => {
      const value = cell.valueString || cell.valueInteger || cell.valueDate;
      return value !== null && value !== undefined;
    });
  };
  const hasAnyResponses = gridData.rows.some((row) => hasRowResponses(row));
  if (!hasAnyResponses) {
    return '';
  }
  const variableComponents = variable.split(/:(.+)/); // split on ':'
  const stringFunction = variableComponents[1];

  const headerStyleRegex = /headerStyle\(([^(),]*)?\)/;
  const headerStyleMatch = stringFunction?.match(headerStyleRegex);

  const { cols = [], rows = [] } = gridData;
  const filteredRows = rows.filter((row) => hasRowResponses(row));
  const template = `
    <table style="border-collapse: collapse; width: 100%; border: 1px solid #ccc">
      <thead style="${headerStyleMatch ? headerStyleMatch[1] : ''}">
        <tr>
          ${cols
            .map(
              (col) => `
            <th style="text-align: left; border: 1px solid #ccc; padding: 8px;">${col || ''}</th>
          `,
            )
            .join('')}
        </tr>
      </thead>
      <tbody>
        ${renderGridRows(filteredRows)}
      </tbody>
    </table>
  `;

  return template;
}

function renderGridRows(rows) {
  return rows
    .map(
      (row) =>
        `
    <tr>
      ${row.map((cell) => `<td style="border: 1px solid #ccc; padding: 8px;">${Object.values(cell)?.[0] || ''}</td>`).join('')}
    </tr>
  `,
    )
    .join('');
}

export function buildResultWithHtmlReportTemplate(
  questionnaire,
  questionnaireResponse,
  questionMap,
  instrumentScores,
  demographic,
  browserTimezone,
) {
  let reportHtmlTemplate = questionnaireUtility.extractHTMLResult(questionnaire, 'htmltemplate-base64');

  const regexMap = new Map([
    ['reportVariables', /{(Questionnaire|QuestionnaireResponse|Demographic)\..*?}/g],
    ['scoringVariables', /{QuestionnaireResponse\.variable\.[^.#:]+(?::[^}]*)?}/],
    ['itemVariables', /{QuestionnaireResponse\.item\.[^.#:]+(?::[^}]*)?}/],
    ['itemScore', /{QuestionnaireResponse\.item\.[^.]+\.score(?::[\w\W]*)?}/],
    ['demographicVariables', /{Demographic\..*?}/],
    ['questionnaireVariables', /{Questionnaire\..*?}/],
    ['questionnaireResponseDate', /{QuestionnaireResponse\.completionDate(?::[\w\W]*)?}/],
    ['itemDisplayCondition', /{QuestionnaireResponse\.item\.[^}.]*\.display}/],
  ]);

  const reportVariablesMatches = reportHtmlTemplate && reportHtmlTemplate?.match(regexMap.get('reportVariables'));
  const variableValuesMap = new Map();

  if (reportVariablesMatches && Array.isArray(reportVariablesMatches) && reportVariablesMatches.length) {
    reportVariablesMatches.forEach((variable) => {
      if (regexMap.get('itemDisplayCondition').test(variable)) {
        variableValuesMap.set(variable, getItemDisplayConditionValue(variable, questionnaire, questionMap));
      } else if (regexMap.get('scoringVariables').test(variable)) {
        variableValuesMap.set(variable, getScoreVariableValue(variable, instrumentScores));
      } else if (regexMap.get('itemScore').test(variable)) {
        variableValuesMap.set(variable, getItemScoreVariableValue(variable, questionnaire, questionMap));
      } else if (regexMap.get('itemVariables').test(variable)) {
        variableValuesMap.set(variable, getItemVariableValue(variable, questionnaire, questionMap));
      } else if (regexMap.get('demographicVariables').test(variable)) {
        variableValuesMap.set(variable, getDemographicVariableValues(variable, demographic));
      } else if (regexMap.get('questionnaireVariables').test(variable)) {
        variableValuesMap.set(variable, getQuestionnaireVariableValues(variable, questionnaire));
      } else if (regexMap.get('questionnaireResponseDate').test(variable)) {
        variableValuesMap.set(variable, getDateVariableValue(variable, questionnaireResponse, browserTimezone));
      }
    });
  }

  reportHtmlTemplate = generateHtmlReport(reportHtmlTemplate, variableValuesMap);

  reportHtmlTemplate = reportHtmlTemplate.replace(/"/g, "'");

  return reportHtmlTemplate;
}

function generateHtmlReport(htmlReportTemplate, parameterMap) {
  const basicHtmlReportStyle = '<style>\n[data-visible="false"] {\n  display: none;\n}\n</style>\n';
  htmlReportTemplate = basicHtmlReportStyle + '\n' + htmlReportTemplate;
  for (let [variable, value] of parameterMap) {
    htmlReportTemplate = htmlReportTemplate.replaceAll(variable, value);
  }
  return htmlReportTemplate;
}

function getDateVariableValue(variable, questionnaireResponse, browserTimezone) {
  /*
    expects a variable like -> {QuestionnaireResponse.completionDate:format(<date_format>)}
    where colon & after characters are optional
  */
  const DEFAULT_DATE_FORMAT = 'yyyy-MM-dd HH:mm:ss';
  let completionDate = getQuestionnaireCompletionDate(questionnaireResponse);
  let formattedDate;

  // Parse the local ISO string without timezone conversion
  if (typeof completionDate === 'string' && completionDate.includes('T')) {
    const [datePart, timePart] = completionDate.split('T');
    const [time] = timePart.split('.');
    formattedDate = `${datePart} ${time}`;
  } else if (browserTimezone) {
    formattedDate = formatInTimeZone(completionDate, browserTimezone, DEFAULT_DATE_FORMAT);
  } else {
    formattedDate = format(completionDate, DEFAULT_DATE_FORMAT);
  }

  if (!variable) return formattedDate;

  let dateComponents = variable.replace('{', '').replace('}', '').split(/:(.+)/);

  if (dateComponents.length > 1) {
    formattedDate = applyStringFunctions(dateComponents[1], completionDate, browserTimezone);
  }

  return formattedDate;
}

function getQuestionnaireVariableValues(variable, questionnaire) {
  if (variable === '{Questionnaire.title}') {
    return questionnaire.title;
  } else if (variable === '{Questionnaire.name}') {
    return questionnaire.name;
  } else if (variable === '{Questionnaire.description}' && questionnaire.description !== 'cannot locate string') {
    return questionnaire.description || '';
  } else {
    return '';
  }
}

function getDemographicVariableValues(variable, demographic) {
  if (variable === '{Demographic.firstName}') {
    return demographic ? demographic.firstName : '';
  } else if (variable === '{Demographic.lastName}') {
    return demographic ? demographic.lastName : '';
  } else if (variable === '{Demographic.gender}') {
    return demographic ? demographic.gender : '';
  } else if (variable === '{Demographic.birthDate}') {
    return demographic ? demographic.dateOfBirth : '';
  } else if (variable === '{Demographic.phn}') {
    return demographic ? demographic.phn : '';
  } else if (variable === '{Demographic.email}') {
    return demographic ? demographic.email : '';
  } else if (variable === '{Demographic.phone}') {
    return demographic ? demographic.phone : '';
  } else if (variable === '{Demographic.address}') {
    return demographic ? demographic.address : '';
  } else {
    return '';
  }
}

function getScoreVariableValue(variable, instrumentScores) {
  let scoreValue = '';

  if (variable && instrumentScores && instrumentScores.length) {
    const scoringVariableName = getScoringVariableName(variable);
    const variableComponents = variable.split(/:(.+)/); //regex to split on first occurrence of  colon ':'
    if (variableComponents.length > 1) {
      let selectedScore = instrumentScores.find((score) => scoringVariableName === score.scoreDefinitionName);

      if (selectedScore) {
        scoreValue = selectedScore.score;
      } else {
        scoreValue = null;
      }

      scoreValue = applyStringFunctions(variableComponents[1], scoreValue);
    } else {
      let selectedScore = instrumentScores.find(
        (score) => variable === `{QuestionnaireResponse.variable.${score.scoreDefinitionName}}`,
      );
      if (selectedScore) {
        scoreValue = selectedScore.score;
      }
    }
  }

  return scoreValue;
}

function getItemScoreVariableValue(reportVariable, questionnaire, questionMap) {
  let answerValue = '';
  const { itemLinkId } = getItemIds(reportVariable, questionnaire);
  let score = questionnaireUtility.getScoreOfAnswers(itemLinkId, questionnaire, questionMap);
  answerValue = score;

  const variableComponents = reportVariable.split(/:(.+)/); //regex to split on first occurrence of  colon ':'
  if (variableComponents.length > 1) {
    answerValue = applyStringFunctions(variableComponents[1], score);
  }

  return answerValue || '';
}

function getItemVariableValue(variable, questionnaire, questionMap) {
  let answerValue = '';
  const { itemId } = getItemIds(variable, questionnaire);

  if (itemId && questionMap) {
    let selectedQuestion = questionMap.get(itemId);
    if (selectedQuestion) {
      if (selectedQuestion.type === 'complex' || selectedQuestion.id?.includes('complex')) {
        const cols = selectedQuestion?.item?.map((col) => col.text);
        const maxRows = Math.max(...(selectedQuestion.item?.map((col) => col?.answer?.length) || []));
        const rows = [];
        for (let i = 0; i < maxRows; i++) {
          const row = selectedQuestion.item?.map((col) => col?.answer?.[i] ?? { valueString: '' });
          rows.push(row);
        }

        const gridHtml = getDataGridHtml(variable, { cols, rows });

        return gridHtml;
      } else {
        answerValue = questionnaireUtility.getQuestionAnswerValue(selectedQuestion);
      }
    }

    const variableComponents = variable.split(/:(.+)/); //regex to split on first occurrence of  colon ':'

    if (variableComponents.length > 1) {
      answerValue = applyStringFunctions(variableComponents[1], answerValue);
    }
  }
  return answerValue;
}

function getItemIds(reportVariable, questionnaire) {
  const variableComponents = reportVariable.split(':');
  const itemLinkId = variableComponents[0].split('.')[2].replace('}', '');
  let itemId = '';

  if (itemLinkId) {
    itemId = questionnaireUtility.getQuestionId(itemLinkId, questionnaire);
  }

  return { itemLinkId, itemId };
}

function getScoringVariableName(reportVariable) {
  const reportVariableComponents = reportVariable.split(':');
  const scoringVariableName = reportVariableComponents[0].split('.')[2].replace('}', '');

  return scoringVariableName;
}

function getArithmeticComparison(_operand1, _operand2, operator) {
  let result = false;
  let operand1 = parseInt(_operand1);
  let operand2 = parseInt(_operand2);

  switch (operator) {
    case 'lt':
      result = operand1 < operand2;
      break;
    case 'le':
      result = operand1 <= operand2;
      break;
    case 'eq':
      result = operand1 === operand2;
      break;
    case 'gt':
      result = operand1 > operand2;
      break;
    case 'ge':
      result = operand1 >= operand2;
      break;
    case 'ne':
      result = operand1 !== operand2;
      break;
    default:
      break;
  }
  return result;
}

function getItemVariableComparison(compareMatch, score) {
  let result = 'false';
  let operator = compareMatch[1];
  let operand1 = score;
  let operand2 = compareMatch[2];

  result = getArithmeticComparison(operand1, operand2, operator);

  return result;
}

function getItemDisplayConditionValue(variable, questionnaire, questionMap) {
  let isConditionMatches = false;
  if (!variable) return isConditionMatches;
  let selectedItemLinkId = variable.split('.')[2];

  if (!questionMap || !questionnaire) return isConditionMatches;
  if (selectedItemLinkId) {
    let questionId = questionnaireUtility.getQuestionId(selectedItemLinkId, questionnaire);
    let selectedItem = questionMap.get(questionId);
    if (selectedItem) {
      isConditionMatches = questionnaireUtility.isQuestionVisible(
        selectedItem.question || selectedItem,
        questionnaire,
        questionMap,
      );
    }
  }

  return isConditionMatches;
}

export function applyStringFunctions(stringFunction, answer, browserTimezone) {
  let answerValue = answer;
  const compareRegex = /compare\(([^,]+),([^)]+)\)/;
  const compareMatch = stringFunction?.match(compareRegex);

  const decimalNumberRegex = /round\((\d+(?:\.\d+)?)?\)/;
  const decimalNumberMatch = stringFunction?.match(decimalNumberRegex);

  const booleanRegex = /boolean\((\d+(?:\.\d+)?)?\)/;
  const booleanMatch = stringFunction?.match(booleanRegex);

  const formatRegex = /format\(([^(),]*)?\)/;
  const formatMatches = stringFunction?.match(formatRegex);

  const equalRegex = /equal\(([^(),]*)?\)/;
  const equalMatch = stringFunction?.match(equalRegex);

  if (formatMatches) {
    try {
      const dateFormat = formatMatches[1].replace('YYYY', 'yyyy').replace('DD', 'dd');

      // Handle our new local ISO string format
      if (typeof answer === 'string' && answer.includes('T')) {
        const [datePart, timePart] = answer.split('T');
        const [year, month, day] = datePart.split('-');
        const [time] = timePart.split('.');
        const [hours, minutes, seconds = 0] = time.split(':');

        // Create Date object using local components (no timezone conversion)
        const parsedDate = new Date(
          parseInt(year),
          parseInt(month) - 1,
          parseInt(day),
          parseInt(hours),
          parseInt(minutes),
          parseInt(seconds),
        );

        answerValue = format(parsedDate, dateFormat);
      } else if (browserTimezone) {
        answerValue = formatInTimeZone(answer, browserTimezone, dateFormat);
      } else {
        answerValue = format(answer, dateFormat);
      }
    } catch (err) {
      answerValue = answer;
      console.error('Invalid DateTime format provided');
    }
  }

  if (compareMatch) {
    answerValue = getItemVariableComparison(compareMatch, answer);
  }

  if (decimalNumberMatch) {
    const decimalNumber = Number(decimalNumberMatch[1]);
    answerValue = Number(answer).toFixed(decimalNumber);
  }

  if (booleanMatch) {
    answerValue = answer === null ? null : Boolean(answer);
  }

  if (equalMatch) {
    const matchString = equalMatch[1];
    answerValue = answer === matchString;
  }

  return answerValue;
}

export function decorateWithHtmlReport(fhirQuestionnaire, fhirQuestionnaireResponse, questionMap) {
  let finalFhirQuestionnaireResponse = { ...fhirQuestionnaireResponse };

  if (!fhirQuestionnaire?.item?.length) return finalFhirQuestionnaireResponse;

  const questionnaireResponse = fhirQuestionnaireResponse;
  const scores = getCalculatedScores(fhirQuestionnaireResponse);

  const htmlReportActiveExtension = extractExtension(fhirQuestionnaire.extension, 'htmltemplate-base64');
  const htmlReportActive = (htmlReportActiveExtension && htmlReportActiveExtension.valueString) || false;

  if (!htmlReportActive) return finalFhirQuestionnaireResponse;

  let generatedHtmlReport = buildResultWithHtmlReportTemplate(
    fhirQuestionnaire,
    questionnaireResponse,
    questionMap,
    scores,
  );
  generatedHtmlReport = generatedHtmlReport.replace(/\n/g, '');
  const convertedXmlString = convertHtmlStringToXmlString(generatedHtmlReport);
  finalFhirQuestionnaireResponse.text = {
    status: 'generated',
    div: convertedXmlString,
  };

  return finalFhirQuestionnaireResponse;
}

export function convertHtmlStringToXmlString(htmlString) {
  const parser = new DOMParser();
  const htmlDoc = parser.parseFromString(htmlString, 'text/html');
  const xmlString = 'root ' + new XMLSerializer().serializeToString(htmlDoc);

  return xmlString;
}

////////
export function buildBannerWithTemplate(demographic, organization, template) {
  const regexMap = new Map([
    ['bannerVariables', /{(Client|Org)\..*?}/g],
    ['clientVariables', /{Client\..*?}/],
    ['organizationVariables', /{Org\..*?}/],
  ]);

  const bannerVariablesMatches = template && template?.match(regexMap.get('bannerVariables'));
  const variableValuesMap = new Map();
  var hasClientVariables = false;
  var hasOrganizationVariables = false;

  if (bannerVariablesMatches && Array.isArray(bannerVariablesMatches) && bannerVariablesMatches.length) {
    bannerVariablesMatches.forEach((variable) => {
      if (regexMap.get('clientVariables').test(variable)) {
        variableValuesMap.set(variable, getClientVariableValues(variable, demographic));
        hasClientVariables = true;
      } else if (regexMap.get('organizationVariables').test(variable)) {
        variableValuesMap.set(variable, getOrganizationVariableValues(variable, organization));
        hasOrganizationVariables = true;
      }
    });
  }

  var bannerTemplate = '';

  if (hasClientVariables && hasOrganizationVariables) {
    bannerTemplate = generateHtmlReport(template, variableValuesMap);
    bannerTemplate = bannerTemplate.replace(/"/g, "'");
  }

  return bannerTemplate;
}

function getClientVariableValues(variable, client) {
  if (variable === '{Client.firstName}') {
    return client ? client.firstName : '';
  } else if (variable === '{Client.lastName}') {
    return client ? client.lastName : '';
  } else if (variable === '{Client.gender}') {
    return client ? client.gender : '';
  } else if (variable === '{Client.birthDate}') {
    return client ? client.dateOfBirth : '';
  } else if (variable === '{Client.phn}') {
    return client ? client.phn : '';
  } else if (variable === '{Client.email}') {
    return client ? client.email : '';
  } else if (variable === '{Client.phone}') {
    return client ? client.phone : '';
  } else if (variable === '{Client.address}') {
    return client ? client.address : '';
  } else {
    return '';
  }
}

function getOrganizationVariableValues(variable, organization) {
  if (variable === '{Org.name}') {
    return organization ? organization.organizationName : '';
  } else if (variable === '{Org.tagline}') {
    return organization ? organization.tagline : '';
  } else if (variable === '{Org.address}') {
    return organization ? organization.address : '';
  } else {
    return '';
  }
}

////////
